# Pantry Pal Project Brief

## 🎯 Project Overview

**Pantry Pal** is a multi-tenant collaborative pantry management platform designed to help individuals and families efficiently manage their food and household product inventories, reduce waste, and promote sustainable living.

### Vision

To become the leading collaborative pantry management platform, making household inventory tracking effortless, significantly reducing food waste, and promoting sustainable living through smart consumption habits.

### Core Goals

* **Reduce Waste & Save Money**: Track expiration dates and consumption patterns to minimize food waste
* **Improve Organization & Efficiency**: Provide intuitive tools for locating items, planning meals, and generating shopping lists
* **Foster Collaboration**: Enable seamless, real-time inventory management among family members or housemates
* **Enhance User Experience**: Offer a user-friendly and reliable application across web and mobile platforms

## 👥 Target Audience

* **Individuals**: Seeking better personal inventory organization and waste reduction
* **Families**: Requiring collaborative system for shared groceries and household essentials
* **Housemates/Co-living Groups**: Managing common food items and expenses
* **Environmentally Conscious Consumers**: Focused on reducing environmental footprint

## 🏗️ Technology Stack

### Backend (Current Implementation)

* **Language**: Go (Golang) 1.20+
* **Web Framework**: Fiber v2
* **Database**: PostgreSQL 14+
* **ORM**: GORM
* **Logging**: Zerolog (structured JSON logging)
* **Configuration**: Koanf (YAML + environment variables)
* **Caching**: Redis 6+
* **Authentication**: JWT with refresh tokens
* **Authorization**: Role-based access control (RBAC)
* **Database Migrations**: golang-migrate/migrate
* **Input Validation**: go-playground/validator/v10
* **API Documentation**: Swagger/OpenAPI

### Frontend (Planned)

* **Framework**: Next.js PWA
* **Language**: TypeScript
* **Styling**: Tailwind CSS
* **State Management**: Zustand or Redux Toolkit

## 📊 Current Status (90% Complete)

### ✅ Completed Features

* **Core Systems**: Authentication, authorization, configuration, logging, error handling
* **User & Pantry Management**: Registration, login, profile management, multi-pantry support
* **Product Catalog**: Hierarchical categories, units of measure with conversions, product variants
* **Inventory Management**: Item tracking, quantity management, expiration monitoring, location tracking
* **Recipe Management**: Recipe storage, ingredient tracking, pantry availability checking
* **Shopping Lists**: List creation, item management, purchase integration
* **Expiration Tracking**: Alerts and notifications for expiring items
* **Password Change**: Secure password updates with token revocation
* **Comprehensive Testing**: 19/19 endpoint groups passing, 37/37 unit tests passing

### 🚧 In Progress (5%)

* **Enhanced Shopping Lists**: Receipt storage, store information, price tracking
* **Pantry-Specific Settings**: User configuration and preferences

### ❌ Planned Features (10%)

* **Account Recovery**: Password reset functionality
* **Usage Tracking & Analytics**: Detailed consumption tracking
* **Inventory Adjustments**: Non-consumption inventory changes
* **Advanced Notifications**: Email, SMS, push notifications
* **Idempotency Middleware**: Request idempotency implementation

## 🏛️ Architecture Principles

### Clean Architecture

* **Domain Layer** (`internal/core/domain`): Business entities and rules
* **Use Case Layer** (`internal/core/usecases`): Application-specific business logic
* **Infrastructure Layer** (`internal/infra`): External concerns (database, web, etc.)
* **Dependencies flow inward**: Infrastructure → Use Cases → Domain

### Key Patterns

* **Repository Pattern**: Data access abstraction
* **Domain Events**: Capture significant business occurrences
* **CQRS-like**: Separate read/write operations where beneficial
* **Multi-tenancy**: All data scoped to pantries with proper isolation
* **Error Handling**: Custom `AppError` types with proper wrapping
* **API Responses**: Standardized JSON structure with pagination support

## 📁 Project Structure

```
pantry-pal/
├── cmd/api/                    # Application entry point
├── internal/
│   ├── core/
│   │   ├── domain/            # Business entities and interfaces
│   │   └── usecases/          # Application business logic
│   └── infra/
│       ├── persistence/       # Database repositories
│       ├── web/              # HTTP handlers and middleware
│       ├── config/           # Configuration management
│       ├── logger/           # Logging setup
│       └── errors/           # Custom error types
├── memory-bank/              # Comprehensive project documentation
│   ├── 01-project/          # Core project documentation
│   ├── 02-architecture/     # Technical architecture & patterns
│   ├── 03-features/         # Feature-specific documentation
│   ├── 04-implementation/   # Implementation guides
│   ├── 05-troubleshooting/  # Bug fixes & debugging
│   └── 06-progress/         # Progress tracking
└── docs/                    # API documentation (Swagger)
```

## 🔒 Security & Quality Standards

### Security

* **JWT Authentication**: Access tokens (15-30 min) + refresh tokens (7-30 days)
* **Role-Based Access Control**: Owner, Admin, Editor, Viewer roles per pantry
* **Input Validation**: Comprehensive validation on all endpoints
* **Error Handling**: Generic error messages to clients, detailed logging internally
* **Password Security**: bcrypt hashing, secure password change flow

### Quality Standards

* **Test Coverage**: >80% for core business logic
* **Code Quality**: golangci-lint compliance, go fmt formatting
* **Documentation**: Comprehensive Swagger/OpenAPI documentation
* **Logging**: Structured logging with request IDs and context
* **Error Handling**: Consistent error types and proper error wrapping

## 🚀 Core Features

### User & Pantry Management

* Secure user registration and authentication
* Multiple pantries per user with role-based collaboration
* Pantry member invitation and management
* Profile management and password change

### Product Catalog

* Hierarchical product categories
* Product variants with barcodes and images
* Units of measure with automatic conversions
* Search and filtering capabilities

### Inventory Management

* Item tracking with quantities, locations, and expiration dates
* Bulk operations for efficiency
* Expiration monitoring with alerts
* Usage tracking and consumption logging

### Recipe & Shopping

* Recipe storage with ingredient tracking
* Pantry availability checking for recipes
* Shopping list generation and management
* Integration between shopping and inventory

### Notifications & Alerts

* In-app notifications for low stock and expiring items
* Pantry invitation notifications
* Configurable alert thresholds

## 📋 Development Guidelines

### Mandatory Standards

1. **MUST** follow Clean Architecture principles
2. **MUST** maintain high test coverage (>80%)
3. **MUST** use established error handling patterns
4. **MUST** implement proper input validation
5. **MUST** follow security guidelines
6. **MUST** maintain multi-tenant data isolation
7. **MUST** update Swagger documentation after API changes
8. **MUST** update memory bank documentation

### API Conventions

* RESTful design with appropriate HTTP methods
* Standardized JSON response format
* Pagination support for list endpoints
* Consistent error response structure
* Proper HTTP status codes

### Database Rules

* Use golang-migrate for schema changes
* Implement soft deletes with `deleted_at`
* Use transactions for multi-table operations
* Follow repository pattern for data access

## 📚 Documentation

The project maintains comprehensive documentation in the `memory-bank/` directory:

* **Project Documentation**: Vision, requirements, and context
* **Architecture**: Technical requirements, patterns, and coding examples
* **Features**: Detailed feature specifications and implementation status
* **Implementation**: Guides and summaries of completed work
* **Troubleshooting**: Bug fixes and debugging guides
* **Progress**: Current status and tracking

## 🎯 Next Priorities

1. **Enhance Shopping Lists with Receipt/Store Features** (2-3 days)
2. **Implement Pantry-Specific Settings** (2-3 days)
3. **Add Account Recovery System** (2-3 days)
4. **Enhance Usage Tracking & Analytics** (2-3 days)
5. **Implement Inventory Adjustments** (2 days)

## 📞 For Developers

### Getting Started

1. Review `memory-bank/01-project/PROJECT_BRIEF.md` for detailed overview
2. Study `memory-bank/02-architecture/DEVELOPMENT_RULES_AND_GUIDES.md` for coding standards
3. Check `memory-bank/06-progress/PROGRESS_TRACKING.md` for current status
4. Follow feature documentation in `memory-bank/03-features/` for specific implementations

### Key Resources

* **Architecture Patterns**: `memory-bank/02-architecture/`
* **Feature Specifications**: `memory-bank/03-features/`
* **Implementation Examples**: `memory-bank/02-architecture/BACKEND_CODING_EXAMPLES.md`
* **Troubleshooting**: `memory-bank/05-troubleshooting/`
* **API Documentation**: Generated Swagger docs in `docs/`

---

*This project brief provides a comprehensive overview for developers, stakeholders, and AI assistants working on the Pantry Pal platform. For detailed technical information, refer to the memory-bank documentation.*
