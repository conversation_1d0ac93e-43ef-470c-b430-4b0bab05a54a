// Package main provides the entry point for the Pantry Pal API server.
//
//	@title			Pantry Pal API
//	@version		2.0
//	@description	A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.
//	@termsOfService	http://swagger.io/terms/
//
//	@contact.name	Pantry Pal API Support
//	@contact.url	http://www.pantrypal.com/support
//	@contact.email	<EMAIL>
//
//	@license.name	MIT
//	@license.url	https://opensource.org/licenses/MIT
//
//	@host		localhost:8080
//	@BasePath	/api/v1
//
//	@securityDefinitions.apikey	BearerAuth
//	@in							header
//	@name						Authorization
//	@description				Type "Bearer" followed by a space and JWT token.
//
//	@tag.name			Authentication
//	@tag.description	User authentication and session management
//
//	@tag.name			Users
//	@tag.description	User profile management
//
//	@tag.name			Pantries
//	@tag.description	Pantry creation and management
//
//	@tag.name			Pantry Memberships
//	@tag.description	Pantry sharing and collaboration
//
//	@tag.name			Categories
//	@tag.description	Product category management
//
//	@tag.name			Products
//	@tag.description	Product catalog management
//
//	@tag.name			Product Variants
//	@tag.description	Specific product variant management
//
//	@tag.name			Units of Measure
//	@tag.description	Unit of measure and conversion management
//
//	@tag.name			Pantry Locations
//	@tag.description	Storage location management within pantries
//
//	@tag.name			Inventory
//	@tag.description	Inventory item tracking and management
//
//	@tag.name			Shopping Lists
//	@tag.description	Shopping list creation and management
//
//	@tag.name			Expiration Tracking
//	@tag.description	Food expiration monitoring and alerts
//
//	@tag.name			Admin
//	@tag.description	Administrative endpoints and system management
package main

import (
	"context"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/wongpinter/pantry-pal/docs" // swagger docs
	"github.com/wongpinter/pantry-pal/internal/infra/config"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
	"github.com/wongpinter/pantry-pal/internal/infra/web"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	logger := logger.New(cfg.Logger)

	// Initialize web server
	server, err := web.NewServer(cfg, logger)
	if err != nil {
		logger.Fatal().Err(err).Msg("Failed to initialize server")
	}

	// Start server in a goroutine
	go func() {
		if err := server.Start(); err != nil {
			logger.Fatal().Err(err).Msg("Failed to start server")
		}
	}()

	logger.Info().
		Str("port", cfg.Server.Port).
		Str("env", cfg.App.Environment).
		Msg("Server started successfully")

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info().Msg("Shutting down server...")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Shutdown server
	if err := server.Shutdown(ctx); err != nil {
		logger.Error().Err(err).Msg("Server forced to shutdown")
	} else {
		logger.Info().Msg("Server shutdown complete")
	}
}
