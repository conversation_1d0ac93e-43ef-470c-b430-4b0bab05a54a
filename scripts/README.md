# Pantry Pal Seed Data Documentation

This directory contains comprehensive seed data for testing and simulation of all Pantry Pal endpoints and scenarios.

## 📁 Seed Files Overview

### 1. `seed-comprehensive-data.sql`

**Core foundation data for all scenarios**
* **Users**: 8 test users with different roles and use cases
* **Stores**: 5 different store types (grocery, wholesale, farmers market, etc.)
* **Pantries**: 6 pantries representing different scenarios
* **Pantry Memberships**: Owner, admin, editor, viewer roles with pending invitations
* **Pantry Locations**: 19 realistic storage locations
* **Products**: 20 products across all major categories
* **Product Variants**: 18 variants with barcodes and packaging details

### 2. `seed-inventory-recipes.sql`

**Inventory and recipe system data**
* **Inventory Items**: 22 items across different pantries with realistic quantities and expiration dates
* **Recipe Tags**: 10 categorization tags (Quick & Easy, Vegetarian, etc.)
* **Recipes**: 5 complete recipes with different complexity levels
* **Recipe Ingredients**: Detailed ingredient lists with quantities and notes
* **Recipe Instructions**: Step-by-step cooking instructions

### 3. `seed-shopping-alerts.sql`

**Shopping and notification data**
* **Shopping Lists**: 4 shopping lists for different scenarios
* **Shopping List Items**: Mix of product variants and free-text items
* **Alert Configurations**: 4 alert setups for expiration tracking
* **Notifications**: Sample notification history

## 🎭 User Personas & Scenarios

### 👤 Test Users

| Username | Email | Role | Scenario |
|----------|-------|------|----------|
| `admin_user` | <EMAIL> | System Admin | Platform administration |
| `john_doe` | <EMAIL> | Home Cook | Personal kitchen management |
| `jane_smith` | <EMAIL> | Apartment Dweller | Small space organization |
| `bob_wilson` | <EMAIL> | Garage Storage | Secondary storage management |
| `alice_brown` | <EMAIL> | Recipe Creator | Recipe development and sharing |
| `chef_mike` | <EMAIL> | Professional Chef | Restaurant inventory management |
| `family_mom` | <EMAIL> | Family Manager | Large family meal planning |
| `family_dad` | <EMAIL> | Family Member | Shared family pantry access |

### 🏠 Pantry Scenarios

| Pantry | Owner | Type | Use Case |
|--------|-------|------|----------|
| Johns Kitchen | john_doe | Personal | Single person household |
| Janes Apartment | jane_smith | Small Space | Apartment living |
| Bobs Garage Fridge | bob_wilson | Secondary | Additional storage |
| Family Kitchen | family_mom | Family | Large family management |
| Restaurant Pantry | chef_mike | Commercial | Professional kitchen |
| Office Break Room | admin_user | Shared | Workplace kitchen |

## 🛒 Product Catalog

### Categories & Products

* **Dairy & Eggs**: Milk, Eggs, Cheese, Yogurt
* **Meat & Seafood**: Chicken, Beef, Salmon
* **Fruits & Vegetables**: Bananas, Apples, Spinach, Tomatoes
* **Pantry Staples**: Olive Oil, Pasta, Rice, Beans
* **Beverages**: Coffee, Orange Juice, Sparkling Water
* **Snacks & Sweets**: Almonds, Dark Chocolate

### Product Variants

Each product includes realistic variants with:
* Specific sizes and packaging (1 gallon, 12 count, 8oz block, etc.)
* Unique barcodes for testing barcode lookup
* Appropriate units of measure
* Packaging types (single, bulk, multi-pack)

## 🍳 Recipe Collection

### Recipe Examples

1. **Perfect Scrambled Eggs** (Easy, 10 min) - Basic breakfast recipe
2. **Quick Chicken Stir Fry** (Medium, 25 min) - Healthy dinner option
3. **Aglio e Olio** (Easy, 25 min) - Classic Italian pasta
4. **Family Rice Bowl** (Medium, 45 min) - Nutritious family meal
5. **Chocolate Almond Bark** (Easy, 10 min) - Simple dessert

Each recipe includes:
* Complete ingredient lists with quantities
* Step-by-step instructions with timing
* Difficulty levels and serving sizes
* Mix of product variants and free-text ingredients

## 🛍️ Shopping & Purchase Scenarios

### Shopping Lists

* **Weekly Groceries** (John) - Regular household shopping
* **Monthly Stock Up** (Family) - Bulk family shopping
* **Restaurant Supplies** (Chef) - Professional ingredient orders
* **Office Snacks** (Admin) - Workplace refreshments

### Shopping List Management

* Realistic shopping scenarios for different user types
* Mix of product variants and free-text items
* Purchase tracking through completed shopping lists

## 🔔 Alert & Notification System

### Alert Configurations

* **Global alerts** - User-wide expiration monitoring
* **Pantry-specific alerts** - Location-based notifications
* **Quiet hours** - Time-based notification scheduling
* **Value thresholds** - Minimum value filtering

### Notification Examples

* Expiration warnings (7 days out)
* Critical alerts (same day expiration)
* Pending notifications (scheduled delivery)

## 🚀 Usage Instructions

### Quick Start

```bash
# Load all comprehensive seed data
make seed-complete
```

### Individual Components

```bash
# Load foundation data only
make seed-comprehensive

# Add inventory and recipes
make seed-inventory-recipes

# Add shopping and alerts
make seed-shopping-alerts
```

### Reset and Reload

```bash
# Reset database and reload all seed data
make seed-reset
```

## 🧪 Testing Scenarios

### Authentication Testing

* Multiple user accounts with different roles
* Pending invitations for membership testing
* Password: `password` for all test users (hashed)

### Inventory Management

* Items with various expiration dates (past, near, future)
* Different quantity levels (full, partial, empty)
* Multiple storage locations per pantry

### Recipe System

* Public and private recipes
* Complex ingredient relationships
* Multi-step cooking instructions

### Shopping Lists

* Active and completed shopping lists
* Mixed product variant and free-text items
* Purchase tracking through shopping list completion

### Expiration Tracking

* Pre-configured alert settings
* Sample notification history
* Items approaching expiration dates

## 📊 Data Statistics

* **8 Users** across different personas
* **6 Pantries** with realistic scenarios
* **19 Storage Locations** for organization testing
* **20 Products** with **18 Variants** for catalog testing
* **22 Inventory Items** with expiration tracking
* **5 Complete Recipes** with ingredients and instructions
* **4 Shopping Lists** with **12 Items** for shopping testing
* **4 Alert Configurations** for notification testing

This comprehensive seed data enables testing of all major Pantry Pal features and provides realistic scenarios for development, testing, and demonstration purposes.
