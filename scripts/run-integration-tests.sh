#!/bin/bash

# Pantry Pal Integration Test Runner
# This script runs comprehensive integration tests for all endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}🧪 $1${NC}"
    echo "=================================="
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        echo -e "${RED}❌ Docker is not running. Please start Docker and try again.${NC}"
        exit 1
    fi
    print_status 0 "Docker is running"
}

# Clean up any existing test containers
cleanup_containers() {
    print_info "Cleaning up any existing test containers..."
    docker ps -a --filter "ancestor=postgres:15-alpine" --format "{{.ID}}" | xargs -r docker rm -f
    print_status 0 "Cleanup completed"
}

# Run specific test suite
run_test_suite() {
    local suite_name=$1
    local test_pattern=$2
    local timeout=${3:-5m}
    
    print_header "Running $suite_name Tests"
    
    if go test -v ./internal/test/integration/... -run "$test_pattern" -timeout="$timeout"; then
        print_status 0 "$suite_name tests passed"
        return 0
    else
        print_status 1 "$suite_name tests failed"
        return 1
    fi
}

# Main execution
main() {
    print_header "Pantry Pal Integration Test Runner"
    
    # Check prerequisites
    print_info "Checking prerequisites..."
    check_docker
    
    # Cleanup
    cleanup_containers
    
    # Initialize test results
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    # Test suites to run
    declare -A test_suites=(
        ["Authentication"]="TestAuthTestSuite"
        ["Categories"]="TestCategoryTestSuite"
        ["Pantries"]="TestPantryTestSuite"
        ["All Endpoints"]="TestAllEndpointsTestSuite"
    )
    
    # Parse command line arguments
    if [ $# -eq 0 ]; then
        # Run all tests
        print_info "Running all test suites..."
        
        for suite_name in "${!test_suites[@]}"; do
            test_pattern="${test_suites[$suite_name]}"
            timeout="10m"
            
            if [ "$suite_name" = "All Endpoints" ]; then
                timeout="15m"
            fi
            
            total_tests=$((total_tests + 1))
            
            if run_test_suite "$suite_name" "$test_pattern" "$timeout"; then
                passed_tests=$((passed_tests + 1))
            else
                failed_tests=$((failed_tests + 1))
            fi
            
            echo ""
        done
    else
        # Run specific test suite
        case $1 in
            "auth"|"authentication")
                run_test_suite "Authentication" "TestAuthTestSuite" "5m"
                ;;
            "category"|"categories")
                run_test_suite "Categories" "TestCategoryTestSuite" "5m"
                ;;
            "pantry"|"pantries")
                run_test_suite "Pantries" "TestPantryTestSuite" "5m"
                ;;
            "all"|"endpoints")
                run_test_suite "All Endpoints" "TestAllEndpointsTestSuite" "15m"
                ;;
            "unit")
                print_info "Running unit tests only..."
                if go test -v ./... -short; then
                    print_status 0 "Unit tests passed"
                else
                    print_status 1 "Unit tests failed"
                    exit 1
                fi
                ;;
            "coverage")
                print_info "Running tests with coverage..."
                if go test -v -coverprofile=coverage.out ./internal/test/integration/...; then
                    go tool cover -html=coverage.out -o coverage.html
                    print_status 0 "Coverage report generated: coverage.html"
                else
                    print_status 1 "Coverage tests failed"
                    exit 1
                fi
                ;;
            "help"|"-h"|"--help")
                echo "Usage: $0 [test_suite]"
                echo ""
                echo "Available test suites:"
                echo "  auth, authentication  - Run authentication tests"
                echo "  category, categories   - Run category management tests"
                echo "  pantry, pantries      - Run pantry management tests"
                echo "  all, endpoints        - Run comprehensive endpoint tests"
                echo "  unit                  - Run unit tests only"
                echo "  coverage              - Run tests with coverage report"
                echo ""
                echo "If no argument is provided, all test suites will be run."
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Unknown test suite: $1${NC}"
                echo "Use '$0 help' to see available options."
                exit 1
                ;;
        esac
    fi
    
    # Print summary if multiple tests were run
    if [ $total_tests -gt 1 ]; then
        echo ""
        print_header "Test Summary"
        echo "Total test suites: $total_tests"
        echo "Passed: $passed_tests"
        echo "Failed: $failed_tests"
        
        if [ $failed_tests -eq 0 ]; then
            print_status 0 "All test suites passed!"
            exit 0
        else
            print_status 1 "Some test suites failed!"
            exit 1
        fi
    fi
}

# Cleanup on exit
trap cleanup_containers EXIT

# Run main function
main "$@"
