-- Corrected Comprehensive Seed Data for Pantry Pal Testing
-- This file creates realistic test data with correct table schemas

-- Clear existing data (in dependency order)
DELETE FROM notifications;
DELETE FROM alert_configurations;
DELETE FROM usage_logs;
DELETE FROM inventory_adjustments;
DELETE FROM shopping_list_items;
DELETE FROM shopping_lists;
DELETE FROM recipe_collection_items;
DELETE FROM recipe_collections;
DELETE FROM recipe_reviews;
DELETE FROM recipe_recipe_tags;
DELETE FROM recipe_tags;
DELETE FROM recipe_nutrition;
DELETE FROM recipe_media;
DELETE FROM recipe_instructions;
DELETE FROM recipe_ingredients;
DELETE FROM recipes;
DELETE FROM inventory_items;
DELETE FROM pantry_locations;
DELETE FROM pantry_memberships;
DELETE FROM pantries;
DELETE FROM product_variants;
DELETE FROM products;
DELETE FROM refresh_tokens;
DELETE FROM users;


-- ============================================================================
-- USERS - Create diverse test users for different scenarios
-- ============================================================================

INSERT INTO users (id, username, email, password_hash, first_name, last_name, profile_picture_url) VALUES 
    ('11111111-1111-1111-1111-111111111111', 'admin_user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'https://example.com/avatars/admin.jpg'),
    ('22222222-2222-2222-2222-222222222222', 'john_doe', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', 'https://example.com/avatars/john.jpg'),
    ('33333333-3333-3333-3333-333333333333', 'jane_smith', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', 'https://example.com/avatars/jane.jpg'),
    ('44444444-4444-4444-4444-444444444444', 'bob_wilson', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Bob', 'Wilson', 'https://example.com/avatars/bob.jpg'),
    ('55555555-5555-5555-5555-555555555555', 'alice_brown', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alice', 'Brown', 'https://example.com/avatars/alice.jpg'),
    ('66666666-6666-6666-6666-666666666666', 'chef_mike', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Chef', 'Mike', 'https://example.com/avatars/chef.jpg');

-- ============================================================================
-- STORES - Create stores for purchase history scenarios
-- ============================================================================

INSERT INTO stores (store_id, name, address, city, country, phone_number, website) VALUES 
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Whole Foods Market', '123 Organic Ave', 'San Francisco', 'USA', '******-0101', 'https://wholefoodsmarket.com'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Safeway', '456 Main St', 'San Francisco', 'USA', '******-0102', 'https://safeway.com'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Trader Joes', '789 Market St', 'San Francisco', 'USA', '******-0103', 'https://traderjoes.com');

-- ============================================================================
-- PANTRIES - Create diverse pantry scenarios
-- ============================================================================

INSERT INTO pantries (pantry_id, name, description, owner_user_id) VALUES 
    ('10000000-0000-0000-0000-000000000001', 'Johns Kitchen', 'My main kitchen pantry', '22222222-2222-2222-2222-222222222222'),
    ('10000000-0000-0000-0000-000000000002', 'Janes Apartment', 'Small apartment kitchen', '33333333-3333-3333-3333-333333333333'),
    ('10000000-0000-0000-0000-000000000003', 'Restaurant Pantry', 'Professional kitchen storage', '66666666-6666-6666-6666-666666666666');

-- ============================================================================
-- PANTRY MEMBERSHIPS - Create membership scenarios
-- ============================================================================

INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    ('20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000003', '66666666-6666-6666-6666-666666666666', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', '33333333-3333-3333-3333-333333333333', 'editor', 'active', '22222222-2222-2222-2222-222222222222');

-- ============================================================================
-- PANTRY LOCATIONS - Create realistic storage locations
-- ============================================================================

INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Main Pantry', 'Large walk-in pantry'),
    ('30000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', 'Refrigerator', 'Main kitchen refrigerator'),
    ('30000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', 'Freezer', 'Chest freezer in basement'),
    ('30000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000002', 'Kitchen Cabinet', 'Upper kitchen cabinets'),
    ('30000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000002', 'Fridge', 'Apartment refrigerator'),
    ('30000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000003', 'Dry Storage', 'Main dry goods storage'),
    ('30000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000003', 'Walk-in Cooler', 'Refrigerated storage');

-- ============================================================================
-- PRODUCTS - Create comprehensive product catalog
-- ============================================================================

-- Get category IDs and create products
INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000001', 'Whole Milk', 'Fresh whole milk', category_id, 'Organic Valley' 
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000002', 'Large Eggs', 'Grade A large eggs', category_id, 'Happy Farms' 
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000003', 'Chicken Breast', 'Boneless skinless chicken breast', category_id, 'Bell & Evans' 
FROM categories WHERE name = 'Meat & Seafood';

INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000004', 'Bananas', 'Fresh bananas', category_id, 'Dole' 
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000005', 'Olive Oil', 'Extra virgin olive oil', category_id, 'California Olive Ranch' 
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand) 
SELECT '40000000-0000-0000-0000-000000000006', 'Coffee', 'Medium roast coffee beans', category_id, 'Starbucks' 
FROM categories WHERE name = 'Beverages';

-- ============================================================================
-- PRODUCT VARIANTS - Create specific product variants with barcodes
-- ============================================================================

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id) 
SELECT '50000000-0000-0000-0000-000000000001', '40000000-0000-0000-0000-000000000001', 'Whole Milk 1 Gallon', '1 gallon container', '1234567890123', 'single', unit_id 
FROM units_of_measure WHERE symbol = 'gal';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id) 
SELECT '50000000-0000-0000-0000-000000000002', '40000000-0000-0000-0000-000000000002', 'Large Eggs 12 Count', 'Dozen large eggs', '2345678901234', 'single', unit_id 
FROM units_of_measure WHERE symbol = 'dz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id) 
SELECT '50000000-0000-0000-0000-000000000003', '40000000-0000-0000-0000-000000000003', 'Chicken Breast 2lb Pack', '2 pound family pack', '5678901234567', 'single', unit_id 
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id) 
SELECT '50000000-0000-0000-0000-000000000004', '40000000-0000-0000-0000-000000000004', 'Bananas 3lb Bunch', '3 pound bunch', '8901234567890', 'single', unit_id 
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id) 
SELECT '50000000-0000-0000-0000-000000000005', '40000000-0000-0000-0000-000000000005', 'Olive Oil 500ml Bottle', '500ml bottle', '1357924680135', 'single', unit_id 
FROM units_of_measure WHERE symbol = 'mL';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000006', '40000000-0000-0000-0000-000000000006', 'Coffee Beans 12oz Bag', '12 ounce bag', '5926037148592', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

-- ============================================================================
-- INVENTORY ITEMS - Create realistic inventory across different pantries
-- ============================================================================

-- Johns Kitchen inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 0.75, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), '30000000-0000-0000-0000-000000000002', '2025-06-20', '2025-06-10', 4.99, 'Opened 3 days ago'),
    ('60000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '30000000-0000-0000-0000-000000000002', '2025-06-25', '2025-06-08', 3.49, 'Used 4 eggs'),
    ('60000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000003', 1.5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000003', '2025-06-18', '2025-06-12', 8.99, 'Frozen chicken breast'),
    ('60000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000005', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), '30000000-0000-0000-0000-000000000001', '2026-12-31', '2025-05-15', 12.99, 'Premium olive oil');

-- Janes Apartment inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000001', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), '30000000-0000-0000-0000-000000000005', '2025-06-22', '2025-06-12', 4.99, 'Full gallon'),
    ('60000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000004', 2.5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000004', '2025-06-16', '2025-06-13', 2.99, 'Ripe bananas');

-- Restaurant Pantry inventory (larger quantities)
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000003', 20, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000007', '2025-06-20', '2025-06-10', 45.00, 'Bulk chicken for restaurant'),
    ('60000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000005', 2000, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), '30000000-0000-0000-0000-000000000006', '2026-12-31', '2025-05-01', 35.00, 'Bulk olive oil'),
    ('60000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000006', 10, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000006', '2027-01-01', '2025-06-01', 8.99, 'Restaurant coffee supply');

-- ============================================================================
-- RECIPE TAGS - Create recipe categorization tags
-- ============================================================================

INSERT INTO recipe_tags (id, name, description) VALUES
    ('70000000-0000-0000-0000-000000000001', 'Quick & Easy', 'Recipes that take 30 minutes or less'),
    ('70000000-0000-0000-0000-000000000002', 'Vegetarian', 'Vegetarian-friendly recipes'),
    ('70000000-0000-0000-0000-000000000003', 'Healthy', 'Nutritious and balanced recipes'),
    ('70000000-0000-0000-0000-000000000004', 'Breakfast', 'Morning meal recipes'),
    ('70000000-0000-0000-0000-000000000005', 'Dinner', 'Evening meal recipes');

-- ============================================================================
-- RECIPES - Create comprehensive recipe collection
-- ============================================================================

-- Recipe 1: Scrambled Eggs (John's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', 'Perfect Scrambled Eggs', 'Creamy and fluffy scrambled eggs', 'easy', 5, 5, 10, 2, true);

-- Recipe 2: Chicken Stir Fry (Jane's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', 'Quick Chicken Stir Fry', 'Healthy chicken and vegetable stir fry', 'medium', 15, 10, 25, 4, true);

-- Recipe 3: Coffee Preparation (Chef Mike's recipe)
INSERT INTO recipes (id, user_id, title, description, difficulty, prep_time, cook_time, total_time, servings, is_public)
VALUES ('80000000-0000-0000-0000-000000000003', '66666666-6666-6666-6666-666666666666', 'Perfect Coffee', 'Professional coffee brewing technique', 'easy', 5, 5, 10, 1, true);

-- ============================================================================
-- SHOPPING LISTS - Create realistic shopping scenarios
-- ============================================================================

-- Johns weekly shopping list
INSERT INTO shopping_lists (id, pantry_id, name, description, status, created_by)
VALUES ('90000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Weekly Groceries', 'Regular weekly grocery shopping', 'active', '22222222-2222-2222-2222-222222222222');

-- Janes shopping list
INSERT INTO shopping_lists (id, pantry_id, name, description, status, created_by)
VALUES ('90000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', 'Apartment Essentials', 'Small apartment grocery needs', 'active', '33333333-3333-3333-3333-333333333333');

-- Restaurant supply list
INSERT INTO shopping_lists (id, pantry_id, name, description, status, created_by)
VALUES ('90000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000003', 'Restaurant Supplies', 'Weekly restaurant ingredient order', 'completed', '66666666-6666-6666-6666-666666666666');

-- ============================================================================
-- SHOPPING LIST ITEMS - Add items to shopping lists
-- ============================================================================

-- Johns weekly shopping list items
INSERT INTO shopping_list_items (id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price)
VALUES
    ('91000000-0000-0000-0000-000000000001', '90000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), 'Need fresh milk', false, 4.99),
    ('91000000-0000-0000-0000-000000000002', '90000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'dz'), 'Running low on eggs', false, 3.49);

-- Add free text items to Johns list
INSERT INTO shopping_list_items (id, shopping_list_id, item_name, quantity, unit_of_measure_id, notes, is_purchased, estimated_price)
VALUES
    ('91000000-0000-0000-0000-000000000003', '90000000-0000-0000-0000-000000000001', 'Bread', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Whole wheat bread', false, 5.98);

-- Janes shopping list items
INSERT INTO shopping_list_items (id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price)
VALUES
    ('91000000-0000-0000-0000-000000000004', '90000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000004', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'For smoothies', false, 2.99);



-- ============================================================================
-- ALERT CONFIGURATIONS - Set up expiration tracking alerts
-- ============================================================================

-- Johns global alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value)
VALUES ('94000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', NULL, true, 7, 3, 1, '["in_app", "email"]', '{"start": "22:00", "end": "08:00"}', '[]', 5.00);

-- Janes apartment alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value)
VALUES ('94000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', '10000000-0000-0000-0000-000000000002', true, 5, 2, 1, '["in_app"]', NULL, '[]', 3.00);

-- Restaurant alert configuration (more aggressive)
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value)
VALUES ('94000000-0000-0000-0000-000000000003', '66666666-6666-6666-6666-666666666666', '10000000-0000-0000-0000-000000000003', true, 3, 1, 1, '["in_app", "email"]', NULL, '[]', 10.00);

-- ============================================================================
-- NOTIFICATIONS - Create sample notification history
-- ============================================================================

-- Recent expiration warning for John
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at)
VALUES ('95000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', '10000000-0000-0000-0000-000000000001', 'expiration_warning', 'in_app', 'medium', 'Items Expiring Soon', 'You have 2 items expiring in the next 7 days in Johns Kitchen', '{"item_count": 2, "pantry_name": "Johns Kitchen"}', 'sent', '2025-06-12 09:00:00+00', '2025-06-12 09:00:15+00');

-- Critical expiration alert for restaurant
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at)
VALUES ('95000000-0000-0000-0000-000000000002', '66666666-6666-6666-6666-666666666666', '10000000-0000-0000-0000-000000000003', 'expiration_critical', 'email', 'critical', 'URGENT: Items Expiring Today', 'Critical: 3 items are expiring today in Restaurant Pantry', '{"item_count": 3, "pantry_name": "Restaurant Pantry"}', 'sent', '2025-06-12 06:00:00+00', '2025-06-12 06:00:30+00');
