-- Shopping Lists, Purchases, and Alert Configuration Seed Data
-- This file contains shopping lists, purchase history, and alert configurations

-- ============================================================================
-- SHOPPING LISTS - Create realistic shopping scenarios
-- ============================================================================

-- Johns weekly shopping list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Weekly Groceries', 'Regular weekly grocery shopping', 'active', '22222222-2222-2222-2222-222222222222');

-- Family monthly shopping list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000004', 'Monthly Stock Up', 'Monthly bulk shopping for family', 'active', '77777777-7777-7777-7777-777777777777');

-- Restaurant supply list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000005', 'Restaurant Supplies', 'Weekly restaurant ingredient order', 'completed', '66666666-6666-6666-6666-666666666666');

-- Office snack list
INSERT INTO shopping_lists (shopping_list_id, pantry_id, name, description, status, created_by) 
VALUES ('90000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000006', 'Office Snacks', 'Monthly office snack restock', 'active', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- SHOPPING LIST ITEMS - Add items to shopping lists
-- ============================================================================

-- Johns weekly shopping list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000001', '90000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), 'Need fresh milk', false, 4.99),
    ('91000000-0000-0000-0000-000000000002', '90000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'dz'), 'Running low on eggs', false, 3.49),
    ('91000000-0000-0000-0000-000000000003', '90000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000008', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'For smoothies', false, 2.99);

-- Add free text items to Johns list
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, item_name, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000004', '90000000-0000-0000-0000-000000000001', 'Bread', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Whole wheat bread', false, 5.98),
    ('91000000-0000-0000-0000-000000000005', '90000000-0000-0000-0000-000000000001', 'Avocados', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'For guacamole', false, 3.96);

-- Family monthly shopping list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000006', '90000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000013', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Bulk rice purchase', false, 19.95),
    ('91000000-0000-0000-0000-000000000007', '90000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000014', 12, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), 'Stock up on beans', false, 23.88),
    ('91000000-0000-0000-0000-000000000008', '90000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000015', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'bag'), 'Coffee for the month', false, 26.97);

-- Restaurant supply list items (completed purchases)
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price, actual_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000009', '90000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000005', 25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Weekly chicken order', true, 125.00, 118.75),
    ('91000000-0000-0000-0000-000000000010', '90000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000011', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'btl'), 'Premium olive oil', true, 64.95, 59.99);

-- Office snack list items
INSERT INTO shopping_list_items (shopping_list_item_id, shopping_list_id, product_variant_id, quantity, unit_of_measure_id, notes, is_purchased, estimated_price) 
VALUES 
    ('91000000-0000-0000-0000-000000000011', '90000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000015', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'bag'), 'Office coffee supply', false, 17.98),
    ('91000000-0000-0000-0000-000000000012', '90000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000017', 5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Healthy office snacks', false, 34.95);





-- ============================================================================
-- ALERT CONFIGURATIONS - Set up expiration tracking alerts
-- ============================================================================

-- Johns global alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', NULL, true, 7, 3, 1, '["in_app", "email"]', '{"start": "22:00", "end": "08:00"}', '[]', 5.00);

-- Johns kitchen-specific alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-000000000002', '22222222-2222-2222-2222-222222222222', '10000000-0000-0000-0000-000000000001', true, 5, 2, 1, '["in_app"]', NULL, '[]', 3.00);

-- Family kitchen alert configuration
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-000000000003', '77777777-7777-7777-7777-777777777777', '10000000-0000-0000-0000-000000000004', true, 10, 5, 2, '["in_app", "email"]', '{"start": "21:00", "end": "07:00"}', '[]', 2.00);

-- Restaurant alert configuration (more aggressive)
INSERT INTO alert_configurations (id, user_id, pantry_id, enabled, warning_days, alert_days, critical_days, channels, quiet_hours, category_filters, min_value) 
VALUES ('94000000-0000-0000-0000-000000000004', '66666666-6666-6666-6666-666666666666', '10000000-0000-0000-0000-000000000005', true, 3, 1, 1, '["in_app", "email"]', NULL, '[]', 10.00);

-- ============================================================================
-- NOTIFICATIONS - Create sample notification history
-- ============================================================================

-- Recent expiration warning for John
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at) 
VALUES ('95000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', '10000000-0000-0000-0000-000000000001', 'expiration_warning', 'in_app', 'medium', 'Items Expiring Soon', 'You have 2 items expiring in the next 7 days in Johns Kitchen', '{"item_count": 2, "pantry_name": "Johns Kitchen"}', 'sent', '2025-06-12 09:00:00+00', '2025-06-12 09:00:15+00');

-- Critical expiration alert for restaurant
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at, sent_at) 
VALUES ('95000000-0000-0000-0000-000000000002', '66666666-6666-6666-6666-666666666666', '10000000-0000-0000-0000-000000000005', 'expiration_critical', 'email', 'critical', 'URGENT: Items Expiring Today', 'Critical: 3 items are expiring today in Restaurant Pantry', '{"item_count": 3, "pantry_name": "Restaurant Pantry"}', 'sent', '2025-06-12 06:00:00+00', '2025-06-12 06:00:30+00');

-- Pending notification for family
INSERT INTO notifications (id, user_id, pantry_id, type, channel, priority, title, message, data, status, scheduled_at) 
VALUES ('95000000-0000-0000-0000-000000000003', '77777777-7777-7777-7777-777777777777', '10000000-0000-0000-0000-000000000004', 'expiration_alert', 'in_app', 'high', 'Items Expiring in 3 Days', 'You have 1 item expiring in 3 days in Family Kitchen', '{"item_count": 1, "pantry_name": "Family Kitchen"}', 'pending', '2025-06-13 08:00:00+00');
