-- Comprehensive Seed Data for Pantry Pal Testing and Simulation
-- This file creates realistic test data for all endpoint scenarios

-- Clear existing data (in dependency order)
DELETE FROM notifications;
DELETE FROM alert_configurations;
DELETE FROM usage_logs;
DELETE FROM inventory_adjustments;
DELETE FROM shopping_list_items;
DELETE FROM shopping_lists;
DELETE FROM recipe_collection_items;
DELETE FROM recipe_collections;
DELETE FROM recipe_reviews;
DELETE FROM recipe_recipe_tags;
DELETE FROM recipe_tags;
DELETE FROM recipe_nutrition;
DELETE FROM recipe_media;
DELETE FROM recipe_instructions;
DELETE FROM recipe_ingredients;
DELETE FROM recipes;
DELETE FROM inventory_items;
DELETE FROM pantry_locations;
DELETE FROM pantry_memberships;
DELETE FROM pantries;
DELETE FROM product_variants;
DELETE FROM products;
DELETE FROM refresh_tokens;
DELETE FROM users;
DELETE FROM stores;

-- Reset sequences if needed
-- Note: PostgreSQL with UUID doesn't need sequence resets

-- ============================================================================
-- USERS - Create diverse test users for different scenarios
-- ============================================================================

-- Main test users with different roles and scenarios
INSERT INTO users (id, username, email, password_hash, first_name, last_name, profile_picture_url) VALUES 
    ('11111111-1111-1111-1111-111111111111', 'admin_user', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Admin', 'User', 'https://example.com/avatars/admin.jpg'),
    ('*************-2222-2222-************', 'john_doe', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John', 'Doe', 'https://example.com/avatars/john.jpg'),
    ('*************-3333-3333-************', 'jane_smith', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Jane', 'Smith', 'https://example.com/avatars/jane.jpg'),
    ('44444444-4444-4444-4444-444444444444', 'bob_wilson', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Bob', 'Wilson', 'https://example.com/avatars/bob.jpg'),
    ('55555555-5555-5555-5555-555555555555', 'alice_brown', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Alice', 'Brown', 'https://example.com/avatars/alice.jpg'),
    ('66666666-6666-6666-6666-666666666666', 'chef_mike', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Chef', 'Mike', 'https://example.com/avatars/chef.jpg'),
    ('77777777-7777-7777-7777-777777777777', 'family_mom', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah', 'Johnson', 'https://example.com/avatars/mom.jpg'),
    ('88888888-8888-8888-8888-888888888888', 'family_dad', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'David', 'Johnson', 'https://example.com/avatars/dad.jpg');

-- ============================================================================
-- STORES - Create stores for purchase history scenarios
-- ============================================================================

INSERT INTO stores (store_id, name, address, city, country, phone_number, website) VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 'Whole Foods Market', '123 Organic Ave', 'San Francisco', 'USA', '******-0101', 'https://wholefoodsmarket.com'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 'Safeway', '456 Main St', 'San Francisco', 'USA', '******-0102', 'https://safeway.com'),
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'Trader Joes', '789 Market St', 'San Francisco', 'USA', '******-0103', 'https://traderjoes.com'),
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'Costco Wholesale', '321 Bulk Blvd', 'San Francisco', 'USA', '******-0104', 'https://costco.com'),
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'Local Farmers Market', '555 Fresh St', 'San Francisco', 'USA', '******-0105', 'https://localmarket.com');

-- ============================================================================
-- PANTRIES - Create diverse pantry scenarios
-- ============================================================================

-- Personal pantries
INSERT INTO pantries (pantry_id, name, description, owner_user_id) VALUES 
    ('10000000-0000-0000-0000-000000000001', 'Johns Kitchen', 'My main kitchen pantry', '*************-2222-2222-************'),
    ('10000000-0000-0000-0000-000000000002', 'Janes Apartment', 'Small apartment kitchen', '*************-3333-3333-************'),
    ('10000000-0000-0000-0000-000000000003', 'Bobs Garage Fridge', 'Secondary storage in garage', '44444444-4444-4444-4444-444444444444'),
    ('10000000-0000-0000-0000-000000000004', 'Family Kitchen', 'Main family kitchen pantry', '77777777-7777-7777-7777-777777777777'),
    ('10000000-0000-0000-0000-000000000005', 'Restaurant Pantry', 'Professional kitchen storage', '66666666-6666-6666-6666-666666666666'),
    ('10000000-0000-0000-0000-000000000006', 'Office Break Room', 'Shared office kitchen', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- PANTRY MEMBERSHIPS - Create membership scenarios for testing
-- ============================================================================

-- Owner memberships (automatic)
INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    ('20000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '*************-2222-2222-************', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000002', '*************-3333-3333-************', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000003', '44444444-4444-4444-4444-444444444444', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000004', '77777777-7777-7777-7777-777777777777', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000005', '66666666-6666-6666-6666-666666666666', 'owner', 'active', NULL),
    ('20000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000006', '11111111-1111-1111-1111-111111111111', 'owner', 'active', NULL);

-- Family sharing memberships
INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    ('20000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000004', '88888888-8888-8888-8888-888888888888', 'admin', 'active', '77777777-7777-7777-7777-777777777777'),
    ('20000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000001', '*************-3333-3333-************', 'editor', 'active', '*************-2222-2222-************'),
    ('20000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000005', '55555555-5555-5555-5555-555555555555', 'editor', 'active', '66666666-6666-6666-6666-666666666666');

-- Pending invitations for testing
INSERT INTO pantry_memberships (pantry_membership_id, pantry_id, user_id, role, status, invited_by_user_id) VALUES 
    ('20000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000002', '44444444-4444-4444-4444-444444444444', 'viewer', 'pending_invitation', '*************-3333-3333-************'),
    ('20000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000006', '*************-2222-2222-************', 'editor', 'pending_invitation', '11111111-1111-1111-1111-111111111111');

-- ============================================================================
-- PANTRY LOCATIONS - Create realistic storage locations
-- ============================================================================

-- Johns Kitchen locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', 'Main Pantry', 'Large walk-in pantry'),
    ('30000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', 'Refrigerator', 'Main kitchen refrigerator'),
    ('30000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', 'Freezer', 'Chest freezer in basement'),
    ('30000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', 'Spice Rack', 'Kitchen spice cabinet'),
    ('30000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001', 'Wine Cellar', 'Temperature controlled wine storage');

-- Janes Apartment locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000002', 'Kitchen Cabinet', 'Upper kitchen cabinets'),
    ('30000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000002', 'Fridge', 'Apartment refrigerator'),
    ('30000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000002', 'Counter', 'Kitchen counter storage');

-- Family Kitchen locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000004', 'Pantry Shelves', 'Main pantry shelving'),
    ('30000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000004', 'Refrigerator', 'Family refrigerator'),
    ('30000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000004', 'Freezer', 'Upright freezer'),
    ('30000000-0000-0000-0000-000000000012', '10000000-0000-0000-0000-000000000004', 'Snack Cabinet', 'Kids snack storage');

-- Restaurant Pantry locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES 
    ('30000000-0000-0000-0000-000000000013', '10000000-0000-0000-0000-000000000005', 'Dry Storage', 'Main dry goods storage'),
    ('30000000-0000-0000-0000-000000000014', '10000000-0000-0000-0000-000000000005', 'Walk-in Cooler', 'Refrigerated storage'),
    ('30000000-0000-0000-0000-000000000015', '10000000-0000-0000-0000-000000000005', 'Walk-in Freezer', 'Frozen storage'),
    ('30000000-0000-0000-0000-000000000016', '10000000-0000-0000-0000-000000000005', 'Prep Station', 'Active prep area storage');

-- Office Break Room locations
INSERT INTO pantry_locations (pantry_location_id, pantry_id, name, description) VALUES
    ('30000000-0000-0000-0000-000000000017', '10000000-0000-0000-0000-000000000006', 'Break Room Fridge', 'Shared refrigerator'),
    ('30000000-0000-0000-0000-000000000018', '10000000-0000-0000-0000-000000000006', 'Snack Cabinet', 'Office snack storage'),
    ('30000000-0000-0000-0000-000000000019', '10000000-0000-0000-0000-000000000006', 'Coffee Station', 'Coffee and tea supplies');

-- ============================================================================
-- PRODUCTS - Create comprehensive product catalog
-- ============================================================================

-- Get category IDs for reference
-- Dairy & Eggs products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000001', 'Whole Milk', 'Fresh whole milk', category_id, 'Organic Valley'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000002', 'Large Eggs', 'Grade A large eggs', category_id, 'Happy Farms'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000003', 'Cheddar Cheese', 'Sharp cheddar cheese', category_id, 'Tillamook'
FROM categories WHERE name = 'Dairy & Eggs';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000004', 'Greek Yogurt', 'Plain Greek yogurt', category_id, 'Fage'
FROM categories WHERE name = 'Dairy & Eggs';

-- Meat & Seafood products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000005', 'Chicken Breast', 'Boneless skinless chicken breast', category_id, 'Bell & Evans'
FROM categories WHERE name = 'Meat & Seafood';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000006', 'Ground Beef', 'Lean ground beef 85/15', category_id, 'Grass Run Farms'
FROM categories WHERE name = 'Meat & Seafood';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000007', 'Salmon Fillet', 'Fresh Atlantic salmon', category_id, 'Wild Planet'
FROM categories WHERE name = 'Meat & Seafood';

-- Fruits & Vegetables products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000008', 'Bananas', 'Fresh bananas', category_id, 'Dole'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000009', 'Apples', 'Honeycrisp apples', category_id, 'Stemilt'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000010', 'Spinach', 'Fresh baby spinach', category_id, 'Organic Girl'
FROM categories WHERE name = 'Fruits & Vegetables';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000011', 'Tomatoes', 'Roma tomatoes', category_id, 'Sunset'
FROM categories WHERE name = 'Fruits & Vegetables';

-- Pantry Staples products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000012', 'Olive Oil', 'Extra virgin olive oil', category_id, 'California Olive Ranch'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000013', 'Pasta', 'Spaghetti pasta', category_id, 'Barilla'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000014', 'Rice', 'Long grain white rice', category_id, 'Lundberg'
FROM categories WHERE name = 'Pantry Staples';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000015', 'Black Beans', 'Canned black beans', category_id, 'Goya'
FROM categories WHERE name = 'Pantry Staples';

-- Beverages products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000016', 'Coffee', 'Medium roast coffee beans', category_id, 'Starbucks'
FROM categories WHERE name = 'Beverages';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000017', 'Orange Juice', 'Fresh squeezed orange juice', category_id, 'Simply Orange'
FROM categories WHERE name = 'Beverages';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000018', 'Sparkling Water', 'Sparkling mineral water', category_id, 'San Pellegrino'
FROM categories WHERE name = 'Beverages';

-- Snacks & Sweets products
INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000019', 'Almonds', 'Raw almonds', category_id, 'Blue Diamond'
FROM categories WHERE name = 'Snacks & Sweets';

INSERT INTO products (product_id, name, description, category_id, brand)
SELECT '40000000-0000-0000-0000-000000000020', 'Dark Chocolate', '70% dark chocolate bar', category_id, 'Lindt'
FROM categories WHERE name = 'Snacks & Sweets';

-- ============================================================================
-- PRODUCT VARIANTS - Create specific product variants with barcodes
-- ============================================================================

-- Dairy & Eggs variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000001', '40000000-0000-0000-0000-000000000001', 'Whole Milk 1 Gallon', '1 gallon container', '1234567890123', 'single', unit_id
FROM units_of_measure WHERE symbol = 'gal';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000002', '40000000-0000-0000-0000-000000000002', 'Large Eggs 12 Count', 'Dozen large eggs', '2345678901234', 'single', unit_id
FROM units_of_measure WHERE symbol = 'dz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000003', '40000000-0000-0000-0000-000000000003', 'Cheddar Cheese 8oz Block', '8 ounce block', '3456789012345', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000004', '40000000-0000-0000-0000-000000000004', 'Greek Yogurt 32oz Container', '32 ounce container', '4567890123456', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

-- Meat & Seafood variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000005', '40000000-0000-0000-0000-000000000005', 'Chicken Breast 2lb Pack', '2 pound family pack', '5678901234567', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000006', '40000000-0000-0000-0000-000000000006', 'Ground Beef 1lb Package', '1 pound ground beef', '6789012345678', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000007', '40000000-0000-0000-0000-000000000007', 'Salmon Fillet 1.5lb', '1.5 pound salmon fillet', '7890123456789', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

-- Fruits & Vegetables variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000008', '40000000-0000-0000-0000-000000000008', 'Bananas 3lb Bunch', '3 pound bunch', '8901234567890', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000009', '40000000-0000-0000-0000-000000000009', 'Honeycrisp Apples 2lb Bag', '2 pound bag', '9012345678901', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000010', '40000000-0000-0000-0000-000000000010', 'Baby Spinach 5oz Container', '5 ounce container', '0123456789012', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

-- Pantry Staples variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000011', '40000000-0000-0000-0000-000000000012', 'Olive Oil 500ml Bottle', '500ml bottle', '1357924680135', 'single', unit_id
FROM units_of_measure WHERE symbol = 'mL';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000012', '40000000-0000-0000-0000-000000000013', 'Spaghetti 1lb Box', '1 pound box', '2468013579246', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000013', '40000000-0000-0000-0000-000000000014', 'White Rice 2lb Bag', '2 pound bag', '3691470258369', 'single', unit_id
FROM units_of_measure WHERE symbol = 'lb';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000014', '40000000-0000-0000-0000-000000000015', 'Black Beans 15oz Can', '15 ounce can', '4815162342481', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

-- Beverages variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000015', '40000000-0000-0000-0000-000000000016', 'Coffee Beans 12oz Bag', '12 ounce bag', '5926037148592', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000016', '40000000-0000-0000-0000-000000000017', 'Orange Juice 64oz Carton', '64 ounce carton', '6037148520370', 'single', unit_id
FROM units_of_measure WHERE symbol = 'fl oz';

-- Snacks variants
INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000017', '40000000-0000-0000-0000-000000000019', 'Raw Almonds 16oz Container', '16 ounce container', '7148520369741', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';

INSERT INTO product_variants (variant_id, product_id, name, description, barcode_gtin, packaging_type, default_unit_of_measure_id)
SELECT '50000000-0000-0000-0000-000000000018', '40000000-0000-0000-0000-000000000020', 'Dark Chocolate Bar 3.5oz', '3.5 ounce bar', '8259630741852', 'single', unit_id
FROM units_of_measure WHERE symbol = 'oz';
