-- Inventory and Recipe Seed Data for Pantry Pal Testing
-- This file contains inventory items, recipes, and related data

-- ============================================================================
-- INVENTORY ITEMS - Create realistic inventory across different pantries
-- ============================================================================

-- Johns Kitchen inventory
INSERT INTO inventory_items (item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes)
VALUES
    ('60000000-0000-0000-0000-000000000001', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 0.75, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), '30000000-0000-0000-0000-000000000002', '2025-06-20', '2025-06-10', 4.99, 'Opened 3 days ago'),
    ('60000000-0000-0000-0000-000000000002', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '30000000-0000-0000-0000-000000000002', '2025-06-25', '2025-06-08', 3.49, 'Used 4 eggs'),
    ('60000000-0000-0000-0000-000000000003', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000003', 6, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000002', '2025-07-15', '2025-06-05', 5.99, 'About 3/4 remaining'),
    ('60000000-0000-0000-0000-000000000004', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000005', 1.5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000003', '2025-06-18', '2025-06-12', 8.99, 'Frozen chicken breast'),
    ('60000000-0000-0000-0000-000000000005', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000011', 400, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), '30000000-0000-0000-0000-000000000001', '2026-12-31', '2025-05-15', 12.99, 'Premium olive oil'),
    ('60000000-0000-0000-0000-000000000006', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000012', 0.8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000001', '2026-03-01', '2025-04-20', 1.99, 'Opened box'),
    ('60000000-0000-0000-0000-000000000007', '10000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000015', 10, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000001', '2027-01-01', '2025-06-01', 8.99, 'Whole bean coffee');

-- Family Kitchen inventory
INSERT INTO inventory_items (inventory_item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes) 
VALUES 
    ('60000000-0000-0000-0000-000000000008', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000001', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'gal'), '30000000-0000-0000-0000-000000000010', '2025-06-22', '2025-06-12', 4.99, 'Full gallon'),
    ('60000000-0000-0000-0000-000000000009', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000002', 12, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '30000000-0000-0000-0000-000000000010', '2025-06-28', '2025-06-10', 3.49, 'Full dozen'),
    ('60000000-0000-0000-0000-000000000010', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000008', 2.5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000009', '2025-06-16', '2025-06-13', 2.99, 'Ripe bananas'),
    ('60000000-0000-0000-0000-000000000011', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000009', 1.8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000010', '2025-06-20', '2025-06-11', 4.99, 'Crisp apples'),
    ('60000000-0000-0000-0000-000000000012', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000013', 1.5, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000009', '2026-08-01', '2025-05-20', 3.99, 'Dry rice storage'),
    ('60000000-0000-0000-0000-000000000013', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000014', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), '30000000-0000-0000-0000-000000000009', '2026-12-01', '2025-06-05', 1.99, 'Canned beans'),
    ('60000000-0000-0000-0000-000000000014', '10000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000017', 12, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000012', '2025-12-01', '2025-06-01', 6.99, 'Kids snack almonds');

-- Restaurant Pantry inventory (larger quantities)
INSERT INTO inventory_items (inventory_item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes) 
VALUES 
    ('60000000-0000-0000-0000-000000000015', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000005', 20, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000015', '2025-06-20', '2025-06-10', 45.00, 'Bulk chicken for restaurant'),
    ('60000000-0000-0000-0000-000000000016', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000006', 15, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000015', '2025-06-18', '2025-06-12', 60.00, 'Ground beef for burgers'),
    ('60000000-0000-0000-0000-000000000017', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000007', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000015', '2025-06-17', '2025-06-13', 120.00, 'Fresh salmon fillets'),
    ('60000000-0000-0000-0000-000000000018', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000011', 2000, (SELECT unit_id FROM units_of_measure WHERE symbol = 'mL'), '30000000-0000-0000-0000-000000000013', '2026-12-31', '2025-05-01', 35.00, 'Bulk olive oil'),
    ('60000000-0000-0000-0000-000000000019', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000012', 10, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000013', '2026-06-01', '2025-04-15', 15.00, 'Bulk pasta'),
    ('60000000-0000-0000-0000-000000000020', '10000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000013', 25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), '30000000-0000-0000-0000-000000000013', '2026-10-01', '2025-03-20', 45.00, 'Restaurant rice supply');

-- Office Break Room inventory
INSERT INTO inventory_items (inventory_item_id, pantry_id, product_variant_id, quantity, unit_of_measure_id, location_id, expiration_date, purchase_date, purchase_price, notes) 
VALUES 
    ('60000000-0000-0000-0000-000000000021', '10000000-0000-0000-0000-000000000006', '50000000-0000-0000-0000-000000000015', 8, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000019', '2026-08-01', '2025-06-01', 8.99, 'Office coffee supply'),
    ('60000000-0000-0000-0000-000000000022', '10000000-0000-0000-0000-000000000006', '50000000-0000-0000-0000-000000000017', 10, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), '30000000-0000-0000-0000-000000000018', '2025-10-01', '2025-06-05', 6.99, 'Office snack almonds');

-- ============================================================================
-- RECIPE TAGS - Create recipe categorization tags
-- ============================================================================

INSERT INTO recipe_tags (recipe_tag_id, name, description) VALUES 
    ('70000000-0000-0000-0000-000000000001', 'Quick & Easy', 'Recipes that take 30 minutes or less'),
    ('70000000-0000-0000-0000-000000000002', 'Vegetarian', 'Vegetarian-friendly recipes'),
    ('70000000-0000-0000-0000-000000000003', 'Healthy', 'Nutritious and balanced recipes'),
    ('70000000-0000-0000-0000-000000000004', 'Family Friendly', 'Recipes kids will love'),
    ('70000000-0000-0000-0000-000000000005', 'Comfort Food', 'Hearty and satisfying dishes'),
    ('70000000-0000-0000-0000-000000000006', 'Low Carb', 'Low carbohydrate recipes'),
    ('70000000-0000-0000-0000-000000000007', 'Gluten Free', 'Gluten-free recipes'),
    ('70000000-0000-0000-0000-000000000008', 'Breakfast', 'Morning meal recipes'),
    ('70000000-0000-0000-0000-000000000009', 'Dinner', 'Evening meal recipes'),
    ('70000000-0000-0000-0000-000000000010', 'Dessert', 'Sweet treats and desserts');

-- ============================================================================
-- RECIPES - Create comprehensive recipe collection
-- ============================================================================

-- Recipe 1: Scrambled Eggs (John's recipe)
INSERT INTO recipes (recipe_id, user_id, title, description, difficulty_level, prep_time_minutes, cook_time_minutes, total_time_minutes, servings, is_public, image_url) 
VALUES ('80000000-0000-0000-0000-000000000001', '22222222-2222-2222-2222-222222222222', 'Perfect Scrambled Eggs', 'Creamy and fluffy scrambled eggs', 'easy', 5, 5, 10, 2, true, 'https://example.com/recipes/scrambled-eggs.jpg');

-- Recipe 2: Chicken Stir Fry (Jane's recipe)
INSERT INTO recipes (recipe_id, user_id, title, description, difficulty_level, prep_time_minutes, cook_time_minutes, total_time_minutes, servings, is_public, image_url) 
VALUES ('80000000-0000-0000-0000-000000000002', '33333333-3333-3333-3333-333333333333', 'Quick Chicken Stir Fry', 'Healthy chicken and vegetable stir fry', 'medium', 15, 10, 25, 4, true, 'https://example.com/recipes/chicken-stir-fry.jpg');

-- Recipe 3: Pasta with Olive Oil (Chef Mike's recipe)
INSERT INTO recipes (recipe_id, user_id, title, description, difficulty_level, prep_time_minutes, cook_time_minutes, total_time_minutes, servings, is_public, image_url) 
VALUES ('80000000-0000-0000-0000-000000000003', '66666666-6666-6666-6666-666666666666', 'Aglio e Olio', 'Classic Italian pasta with garlic and olive oil', 'easy', 10, 15, 25, 4, true, 'https://example.com/recipes/aglio-olio.jpg');

-- Recipe 4: Family Rice Bowl (Family recipe)
INSERT INTO recipes (recipe_id, user_id, title, description, difficulty_level, prep_time_minutes, cook_time_minutes, total_time_minutes, servings, is_public, image_url) 
VALUES ('80000000-0000-0000-0000-000000000004', '77777777-7777-7777-7777-777777777777', 'Family Rice Bowl', 'Nutritious rice bowl with vegetables and protein', 'medium', 20, 25, 45, 6, false, 'https://example.com/recipes/rice-bowl.jpg');

-- Recipe 5: Chocolate Almond Treat (Alice's dessert)
INSERT INTO recipes (recipe_id, user_id, title, description, difficulty_level, prep_time_minutes, cook_time_minutes, total_time_minutes, servings, is_public, image_url)
VALUES ('80000000-0000-0000-0000-000000000005', '55555555-5555-5555-5555-555555555555', 'Chocolate Almond Bark', 'Simple chocolate and almond dessert', 'easy', 10, 0, 10, 8, true, 'https://example.com/recipes/chocolate-bark.jpg');

-- ============================================================================
-- RECIPE INGREDIENTS - Add ingredients for each recipe
-- ============================================================================

-- Scrambled Eggs ingredients
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, product_variant_id, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000001', '80000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000002', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Large eggs', false),
    ('81000000-0000-0000-0000-000000000002', '80000000-0000-0000-0000-000000000001', '50000000-0000-0000-0000-000000000001', 0.25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'fl oz'), 'For creaminess', false);

-- Add free text ingredients for scrambled eggs
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, ingredient_name, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000003', '80000000-0000-0000-0000-000000000001', 'Butter', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), '2 tablespoons', false),
    ('81000000-0000-0000-0000-000000000004', '80000000-0000-0000-0000-000000000001', 'Salt', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Pinch of salt', false),
    ('81000000-0000-0000-0000-000000000005', '80000000-0000-0000-0000-000000000001', 'Black Pepper', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'To taste', true);

-- Chicken Stir Fry ingredients
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, product_variant_id, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000006', '80000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000005', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Cut into strips', false),
    ('81000000-0000-0000-0000-000000000007', '80000000-0000-0000-0000-000000000002', '50000000-0000-0000-0000-000000000011', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'fl oz'), 'For cooking', false);

INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, ingredient_name, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000008', '80000000-0000-0000-0000-000000000002', 'Bell Peppers', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Mixed colors', false),
    ('81000000-0000-0000-0000-000000000009', '80000000-0000-0000-0000-000000000002', 'Soy Sauce', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'fl oz'), 'Low sodium preferred', false),
    ('81000000-0000-0000-0000-000000000010', '80000000-0000-0000-0000-000000000002', 'Garlic', 3, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Cloves, minced', false);

-- Pasta Aglio e Olio ingredients
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, product_variant_id, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000011', '80000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000012', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Spaghetti pasta', false),
    ('81000000-0000-0000-0000-000000000012', '80000000-0000-0000-0000-000000000003', '50000000-0000-0000-0000-000000000011', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'fl oz'), 'Extra virgin olive oil', false);

INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, ingredient_name, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000013', '80000000-0000-0000-0000-000000000003', 'Garlic', 6, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Cloves, thinly sliced', false),
    ('81000000-0000-0000-0000-000000000014', '80000000-0000-0000-0000-000000000003', 'Red Pepper Flakes', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'To taste', true),
    ('81000000-0000-0000-0000-000000000015', '80000000-0000-0000-0000-000000000003', 'Parsley', 0.25, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Fresh, chopped', false);

-- Family Rice Bowl ingredients
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, product_variant_id, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000016', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000013', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Long grain white rice', false),
    ('81000000-0000-0000-0000-000000000017', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000014', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'can'), 'Black beans', false),
    ('81000000-0000-0000-0000-000000000018', '80000000-0000-0000-0000-000000000004', '50000000-0000-0000-0000-000000000005', 1, (SELECT unit_id FROM units_of_measure WHERE symbol = 'lb'), 'Grilled chicken', false);

-- Chocolate Almond Bark ingredients
INSERT INTO recipe_ingredients (recipe_ingredient_id, recipe_id, product_variant_id, quantity, unit_of_measure_id, notes, is_optional)
VALUES
    ('81000000-0000-0000-0000-000000000019', '80000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000018', 2, (SELECT unit_id FROM units_of_measure WHERE symbol = 'pc'), 'Dark chocolate bars', false),
    ('81000000-0000-0000-0000-000000000020', '80000000-0000-0000-0000-000000000005', '50000000-0000-0000-0000-000000000017', 4, (SELECT unit_id FROM units_of_measure WHERE symbol = 'oz'), 'Chopped almonds', false);

-- ============================================================================
-- RECIPE INSTRUCTIONS - Add step-by-step instructions
-- ============================================================================

-- Scrambled Eggs instructions
INSERT INTO recipe_instructions (recipe_instruction_id, recipe_id, step_number, instruction, estimated_time_minutes)
VALUES
    ('82000000-0000-0000-0000-000000000001', '80000000-0000-0000-0000-000000000001', 1, 'Crack eggs into a bowl and whisk with milk, salt, and pepper until well combined.', 2),
    ('82000000-0000-0000-0000-000000000002', '80000000-0000-0000-0000-000000000001', 2, 'Heat butter in a non-stick pan over medium-low heat.', 1),
    ('82000000-0000-0000-0000-000000000003', '80000000-0000-0000-0000-000000000001', 3, 'Pour egg mixture into the pan and let sit for 20 seconds.', 1),
    ('82000000-0000-0000-0000-000000000004', '80000000-0000-0000-0000-000000000001', 4, 'Gently stir with a spatula, pushing eggs from edges to center. Repeat until eggs are just set.', 3),
    ('82000000-0000-0000-0000-000000000005', '80000000-0000-0000-0000-000000000001', 5, 'Remove from heat while eggs are still slightly wet. They will continue cooking. Serve immediately.', 1);

-- Chicken Stir Fry instructions
INSERT INTO recipe_instructions (recipe_instruction_id, recipe_id, step_number, instruction, estimated_time_minutes)
VALUES
    ('82000000-0000-0000-0000-000000000006', '80000000-0000-0000-0000-000000000002', 1, 'Cut chicken breast into thin strips and season with salt and pepper.', 5),
    ('82000000-0000-0000-0000-000000000007', '80000000-0000-0000-0000-000000000002', 2, 'Slice bell peppers and mince garlic. Prepare all vegetables.', 8),
    ('82000000-0000-0000-0000-000000000008', '80000000-0000-0000-0000-000000000002', 3, 'Heat olive oil in a large wok or skillet over high heat.', 2),
    ('82000000-0000-0000-0000-000000000009', '80000000-0000-0000-0000-000000000002', 4, 'Add chicken strips and cook until golden brown, about 4-5 minutes.', 5),
    ('82000000-0000-0000-0000-000000000010', '80000000-0000-0000-0000-000000000002', 5, 'Add vegetables and garlic, stir-fry for 3-4 minutes until crisp-tender.', 4),
    ('82000000-0000-0000-0000-000000000011', '80000000-0000-0000-0000-000000000002', 6, 'Add soy sauce and toss everything together. Cook for 1 more minute and serve.', 1);
