# Pantry Pal Project Brief

## Vision
To become the leading collaborative pantry management platform, making household inventory tracking effortless, significantly reducing food waste, and promoting sustainable living through smart consumption habits.

## Core Purpose
A multi-tenant application designed to help individuals and families efficiently manage their food and household product inventories.

## Goals
- **Reduce Waste & Save Money**: Enable users to track expiration dates and consumption patterns to minimize food waste and optimize purchasing
- **Improve Organization & Efficiency**: Provide intuitive tools for locating items, planning meals, and generating shopping lists
- **Foster Collaboration**: Facilitate seamless, real-time inventory management among family members or housemates within shared pantries
- **Enhance User Experience**: Offer a user-friendly and reliable application across web and mobile platforms

## Target Audience
- **Individuals**: Seeking better personal inventory organization and waste reduction
- **Families (Couples, Households with Children)**: Requiring a collaborative system for shared groceries and household essentials
- **Housemates/Co-living Groups**: Desiring a shared tool for managing common food items and expenses
- **Environmentally Conscious Consumers**: Focused on reducing their environmental footprint by minimizing food waste

## Project Scope - Version 2.0 (MVP + Phase 1)

### In-Scope Features:
- **User Management**: Secure registration, login (email/password), profile management, account recovery
- **Pantry Management**: Creation of multiple pantries per user, editing pantry details
- **Pantry Sharing & Collaboration**: Invitation of members to pantries, role-based access control (Owner, Admin, Editor, Viewer), membership status management, ownership transfer
- **Product Catalog**: Centralized catalog of generic products and specific product variants (with barcodes, images). Ability for users to define custom products/variants
- **Category Management**: Hierarchical categorization of products
- **Unit of Measure Management**: Pre-defined units, support for conversions (e.g., liters to milliliters, pounds to grams)
- **Pantry Location Management**: Custom storage locations within each pantry
- **Inventory Tracking**: Adding/updating/removing inventory items with quantity, unit, expiration/best-before dates, and location. Filtering/sorting capabilities
- **Purchase History**: Recording purchase transactions (date, total, store, items bought). Linking purchase items to inventory
- **Usage Tracking**: Logging consumption of inventory items, reducing quantities
- **Inventory Adjustments**: Tracking non-consumption changes (e.g., spoilage, manual corrections)
- **Shopping Lists**: Creating and managing multiple shopping lists, adding product variants or free-text items, marking items as purchased, automatically adding purchased items to inventory
- **Recipe Management**: Storing recipes (name, instructions, servings, prep/cook time, source), detailing ingredients with quantities/units, indicating pantry stock availability for ingredients. Recipes can be personal or shared within a pantry
- **Pantry-Specific Settings**: Configurable thresholds for low stock, default locations, preferred units, currency settings
- **Notifications**: In-app alerts for low stock, expiring/expired items, new pantry invitations

### Out-of-Scope for Version 2.0:
- Advanced notification channels (Email, SMS, Push Notifications – focus on in-app)
- Deep integrations with external APIs (e.g., automatic barcode lookup for new products, smart home devices)
- AI/ML features (e.g., predictive consumption, recipe suggestions based on expiring items)
- Complex analytics dashboards (beyond basic list views and simple totals)
- Receipt OCR scanning
- Subscription or billing management
- Global public recipe sharing (beyond pantry-level sharing)

## Technology Stack

### Backend
- **Programming Language**: Go (Golang) - latest stable version (1.20+)
- **Web Framework**: Fiber v2
- **Database**: PostgreSQL - latest stable version (14+)
- **ORM**: GORM (github.com/go-gorm/gorm)
- **Logging**: Zerolog (github.com/rs/zerolog)
- **Configuration**: Koanf (github.com/knadh/koanf/v2)
- **Caching**: Redis - latest stable version (6+)
- **Authentication**: JWT (github.com/golang-jwt/jwt/v5)
- **Authorization**: Casbin (github.com/casbin/casbin/v2)
- **Database Migrations**: golang-migrate/migrate
- **Input Validation**: github.com/go-playground/validator/v10

### Frontend (Future)
- **Framework**: Next.js PWA
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Zustand or Redux Toolkit

## Project Status
- **Current Phase**: Initial Development
- **Next Milestone**: MVP Release (Version 2.0)
- **Implementation Progress**: 80% complete
- **Test Coverage**: 19/19 endpoint groups passing with comprehensive integration tests

## Key Development Standards
1. **MUST** follow Clean Architecture principles
2. **MUST** maintain high test coverage (>80%)
3. **MUST** document all changes in memory bank
4. **MUST** use established patterns and practices
5. **MUST** follow security guidelines
6. **MUST** maintain multi-tenant isolation

## Documentation Structure
- **Architecture**: [`02-architecture/`](../02-architecture/) - Technical architecture and patterns
- **Features**: [`03-features/`](../03-features/) - Feature-specific documentation
- **Implementation**: [`04-implementation/`](../04-implementation/) - Implementation guides and summaries
- **Troubleshooting**: [`05-troubleshooting/`](../05-troubleshooting/) - Bug fixes and debugging guides
- **Progress**: [`06-progress/`](../06-progress/) - Progress tracking and status

## Future Considerations & Enhancements
- User Preferences (more granular settings)
- Advanced Notification Channels (Email, SMS, Push)
- External API Integrations (Barcode scanning, Smart Home)
- AI/ML Powered Features (Predictive consumption, Smart recipe suggestions)
- Receipt OCR Scanning
- Subscription/Billing Management
- Comprehensive Reporting & Analytics
- Meal Planning
- Global Public Recipe Sharing
- Gamification

---

*For detailed technical requirements, architecture guidelines, and implementation details, refer to the respective documentation in the memory bank subdirectories.*
