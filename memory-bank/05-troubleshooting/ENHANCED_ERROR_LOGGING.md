# Enhanced Error Logging Usage Guide

This guide demonstrates how to use the enhanced error logging system across all application layers for better debugging and monitoring.

## Overview

The enhanced error logging system provides:
- **Layer-specific logging**: Different logging methods for each application layer
- **Rich context**: Comprehensive error context including user, entity, and operation details
- **Automatic categorization**: Errors are automatically categorized by type
- **Global accessibility**: Can be used from anywhere in the application
- **Performance tracking**: Optional performance context for slow operations

## Quick Start

### 1. Initialize Global Error Logger

In your application startup (already done in `server.go`):

```go
// Initialize global error logger
logger.SetGlobalErrorLogger(log)
```

### 2. Basic Usage

```go
import "github.com/wongpinter/pantry-pal/internal/infra/logger"

// Quick error logging with minimal context
logger.QuickLogError(err, "user_service", "create_user")

// Enhanced error logging with full context
logger.LogError(err, logger.ErrorContext{
    Component: "user_service",
    Operation: "create_user",
    UserID:    userID,
    EntityID:  user.ID.String(),
    Additional: map[string]interface{}{
        "email": user.Email,
    },
})
```

## Layer-Specific Usage

### 1. Repository Layer

```go
// In repository methods
func (r *UserRepository) Create(user *domain.User) error {
    if err := r.db.Create(model).Error; err != nil {
        // Enhanced repository error logging
        logger.LogRepositoryError(err, "user", "create", logger.ErrorContext{
            EntityID: user.ID.String(),
            Additional: map[string]interface{}{
                "user_email":    user.Email,
                "user_username": user.Username,
                "table":         "users",
            },
        })
        return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create user")
    }
    return nil
}

// Quick repository logging
func (r *UserRepository) GetByID(id uuid.UUID) (*domain.User, error) {
    if err := r.db.Where("id = ?", id).First(&model).Error; err != nil {
        if err == gorm.ErrRecordNotFound {
            // Expected error - use quick logging
            logger.QuickLogRepositoryError(err, "user", "get_by_id_not_found")
            return nil, errors.New(errors.ErrCodeNotFound, "user not found")
        }
        
        // Unexpected error - use enhanced logging
        logger.LogRepositoryError(err, "user", "get_by_id", logger.ErrorContext{
            EntityID: id.String(),
            Additional: map[string]interface{}{
                "lookup_type": "id",
                "query":       "SELECT * FROM users WHERE id = ?",
            },
        })
        return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user")
    }
    return model.ToDomain(), nil
}
```

### 2. Use Case Layer

```go
// In use case methods
func (uc *InventoryUsecase) CreateInventoryItem(ctx context.Context, userID, pantryID uuid.UUID, req *domain.CreateInventoryItemRequest) (*domain.InventoryItemResponse, error) {
    // Authorization check error
    canAdd, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionCreateItems)
    if err != nil {
        logger.LogUsecaseError(err, "inventory_usecase", "check_create_permission", logger.ErrorContext{
            UserID:   userID.String(),
            EntityID: pantryID.String(),
            Entity:   "pantry",
            Additional: map[string]interface{}{
                "permission": "create_items",
                "operation":  "create_inventory_item",
            },
        })
        return nil, errors.ErrInternalError
    }

    // Repository operation error
    if err := uc.inventoryRepo.Create(item); err != nil {
        logger.LogUsecaseError(err, "inventory_usecase", "create_inventory_item", logger.ErrorContext{
            UserID:   userID.String(),
            EntityID: item.ID.String(),
            Entity:   "inventory_item",
            Additional: map[string]interface{}{
                "pantry_id":            pantryID.String(),
                "product_variant_id":   req.ProductVariantID.String(),
                "quantity":             req.Quantity,
                "repository_operation": "create",
            },
        })
        return nil, err
    }
    
    return response, nil
}
```

### 3. Service Layer

```go
// In service methods
func (s *NotificationService) SendEmail(userID string, template string, data map[string]interface{}) error {
    if err := s.emailProvider.Send(email); err != nil {
        logger.LogServiceError(err, "notification_service", "send_email", logger.ErrorContext{
            UserID: userID,
            Additional: map[string]interface{}{
                "template":      template,
                "recipient":     email.To,
                "provider":      "smtp",
                "retry_attempt": retryCount,
            },
        })
        return errors.Wrap(err, errors.ErrCodeExternalServiceError, "failed to send email")
    }
    return nil
}
```

### 4. Handler Layer

```go
// In HTTP handlers (using the error helper)
func (h *InventoryHandler) CreateInventoryItem(c *fiber.Ctx) error {
    response, err := h.inventoryUsecase.CreateInventoryItem(c.Context(), userID, pantryID, &req)
    if err != nil {
        return handler.LogAndReturnError(c, h.logger, err, handler.ErrorLogContext{
            Operation:   "create_inventory_item",
            UserID:      userIDStr,
            Resource:    "inventory_item",
            RequestData: req,
            Additional: map[string]interface{}{
                "pantry_id":          pantryIDStr,
                "product_variant_id": req.ProductVariantID,
            },
        })
    }
    return handler.CreatedResponse(c, response, "Inventory item created successfully")
}
```

### 5. Infrastructure Layer

```go
// Database connections
func ConnectToDatabase(config DatabaseConfig) (*gorm.DB, error) {
    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    if err != nil {
        logger.LogDatabaseConnectionError(err, config.Database, logger.ErrorContext{
            Additional: map[string]interface{}{
                "host":     config.Host,
                "port":     config.Port,
                "database": config.Database,
                "ssl_mode": config.SSLMode,
            },
        })
        return nil, err
    }
    return db, nil
}

// Cache operations
func (c *RedisCache) Get(key string) (string, error) {
    value, err := c.client.Get(ctx, key).Result()
    if err != nil {
        if err == redis.Nil {
            // Cache miss - expected
            return "", ErrCacheMiss
        }
        
        logger.LogCacheError(err, "get", key, logger.ErrorContext{
            Additional: map[string]interface{}{
                "cache_type": "redis",
                "operation":  "get",
            },
        })
        return "", err
    }
    return value, nil
}

// File operations
func SaveFile(path string, data []byte) error {
    if err := os.WriteFile(path, data, 0644); err != nil {
        logger.LogFileSystemError(err, "write", path, logger.ErrorContext{
            Additional: map[string]interface{}{
                "file_size": len(data),
                "operation": "write",
            },
        })
        return err
    }
    return nil
}

// Network operations
func CallExternalAPI(endpoint string, payload interface{}) error {
    resp, err := http.Post(endpoint, "application/json", body)
    if err != nil {
        logger.LogNetworkError(err, "http_post", endpoint, logger.ErrorContext{
            Additional: map[string]interface{}{
                "method":      "POST",
                "content_type": "application/json",
                "timeout":     "30s",
            },
        })
        return err
    }
    return nil
}
```

## Specialized Error Logging

### Performance Issues

```go
func SlowOperation() error {
    start := time.Now()
    
    // ... operation ...
    
    duration := time.Since(start)
    threshold := 5 * time.Second
    
    if duration > threshold {
        err := fmt.Errorf("operation exceeded threshold")
        logger.LogPerformanceError(err, "slow_database_query", duration, threshold, logger.ErrorContext{
            Additional: map[string]interface{}{
                "query":      "SELECT * FROM large_table",
                "table_size": "10M rows",
            },
        })
    }
    
    return nil
}
```

### Configuration Errors

```go
func LoadConfiguration() error {
    if err := viper.ReadInConfig(); err != nil {
        logger.LogConfigurationError(err, "app_config", logger.ErrorContext{
            Additional: map[string]interface{}{
                "config_file": "config.yaml",
                "config_path": "/etc/app/",
            },
        })
        return err
    }
    return nil
}
```

### Startup Errors

```go
func InitializeApplication() error {
    if err := initDatabase(); err != nil {
        logger.LogStartupError(err, "database_initialization", logger.ErrorContext{
            Additional: map[string]interface{}{
                "phase": "startup",
                "step":  "database_connection",
            },
        })
        return err
    }
    return nil
}
```

## Context Builders

Use context builders for common scenarios:

```go
// User context
userCtx := logger.WithUserContext(userID, requestID)

// Entity context
entityCtx := logger.WithEntityContext("inventory_item", itemID)

// Performance context
perfCtx := logger.WithPerformanceContext(duration)

// Additional context
additionalCtx := logger.WithAdditionalContext(map[string]interface{}{
    "custom_field": "value",
})

// Combine contexts
combinedCtx := logger.CombineContexts(userCtx, entityCtx, perfCtx)

// Use combined context
logger.LogError(err, combinedCtx)
```

## Best Practices

1. **Use appropriate logging level**: Repository errors for expected issues (like not found), enhanced logging for unexpected issues
2. **Include relevant context**: Always include user ID, entity ID, and operation-specific data
3. **Don't log sensitive data**: Avoid logging passwords, tokens, or personal information
4. **Use consistent operation names**: Use descriptive, consistent operation names across the application
5. **Log at the right layer**: Log errors where they occur, not where they're handled
6. **Include performance context**: For operations that might be slow, include timing information
7. **Use error categorization**: Let the system automatically categorize errors for better monitoring

## Monitoring and Alerting

The enhanced error logging provides structured data that can be used for:

- **Error rate monitoring**: Track error rates by category, component, and operation
- **Performance monitoring**: Identify slow operations and performance degradation
- **User impact analysis**: Understand which users are affected by errors
- **Root cause analysis**: Rich context helps identify the root cause of issues
- **Automated alerting**: Set up alerts based on error categories and patterns
