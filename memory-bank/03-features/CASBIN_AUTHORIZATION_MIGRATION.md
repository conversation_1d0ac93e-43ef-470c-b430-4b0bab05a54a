# Casbin Authorization Migration

## Overview
This document outlines the plan for migrating the existing custom authorization system to Casbin for more robust and flexible role-based access control (RBAC).

## Key Features & Requirements

### Casbin Integration (TODO)
- **Casbin Enforcer Setup**: Set up and configure the Casbin enforcer in the application.
- **Policy Management**: Define and manage authorization policies using Casbin's model and policies.
- **Migration from Current System**: Migrate existing authorization logic and data from the custom system (`internal/infra/auth/pantry_authorization_service.go`) to Casbin.

### Policy Management (TODO)
- **Dynamic Policy Updates**: Implement mechanisms for dynamic updates of Casbin policies (e.g., via API or administrative interface).
- **Role-Based Policy Definitions**: Define clear role-based policies for different pantry member roles (Owner, Ad<PERSON>, Editor, Viewer).

## Related Entities
- `CasbinRule` (conceptual entity for Casbin policies)
- [`PantryMembership`](internal/core/domain/pantry_membership.go)

## Implementation Notes
- The current custom authorization system is functional but Casbin offers more flexibility and industry-standard features.
- The migration should be phased to minimize disruption.
- Ensure all existing authorization checks are correctly translated to Casbin policies.
- Consider performance implications of Casbin policy evaluation.