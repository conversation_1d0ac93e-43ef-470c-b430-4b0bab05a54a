# Shopping List Implementation Guide

## Overview

This document provides a comprehensive guide to the Shopping List implementation in the Pantry Pal application. The shopping list feature allows users to create and manage shopping lists within their pantries, track purchase status, and maintain organized shopping workflows.

## Implementation Architecture

### Domain-Driven Design

The shopping list implementation follows Clean Architecture principles with clear separation of concerns:

```
Domain Layer (Business Logic)
├── Entities: ShoppingList, ShoppingListItemEntity
├── Value Objects: ShoppingListStatus, ShoppingListStats
├── Repositories: ShoppingListRepository interface
├── Services: ShoppingListService interface
└── Events: Domain events for shopping list operations

Application Layer (Use Cases)
├── ShoppingListService: Business logic orchestration
├── Authorization: Pantry access control integration
└── Validation: Request validation and business rules

Infrastructure Layer (Technical Details)
├── GORM Repository: Database persistence implementation
├── HTTP Handlers: REST API endpoints (future)
└── Database Migrations: Schema management
```

### Key Design Decisions

1. **Flexible Item Types**: Support both product catalog references and free-text items
2. **Pantry-Scoped**: All shopping lists belong to specific pantries with proper access control
3. **Status Management**: Three-state lifecycle (active → completed → archived)
4. **Purchase Tracking**: Automatic timestamping and status management
5. **Bulk Operations**: Efficient batch operations for mobile and web interfaces

## Database Schema

### Shopping Lists Table

```sql
CREATE TABLE shopping_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'active' 
        CHECK (status IN ('active', 'completed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

### Shopping List Items Table

```sql
CREATE TABLE shopping_list_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shopping_list_id UUID NOT NULL REFERENCES shopping_lists(id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(variant_id) ON DELETE SET NULL,
    free_text_name VARCHAR(255),
    quantity_desired DECIMAL(10,3) NOT NULL CHECK (quantity_desired > 0),
    unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
    notes TEXT,
    is_purchased BOOLEAN NOT NULL DEFAULT FALSE,
    purchased_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Business rule constraints
    CONSTRAINT check_item_reference CHECK (
        (product_variant_id IS NOT NULL AND free_text_name IS NULL) OR
        (product_variant_id IS NULL AND free_text_name IS NOT NULL)
    ),
    CONSTRAINT check_purchased_at CHECK (
        (is_purchased = FALSE AND purchased_at IS NULL) OR
        (is_purchased = TRUE AND purchased_at IS NOT NULL)
    )
);
```

### Key Constraints

1. **Item Reference Validation**: Each item must have either a product variant ID or free text name, but not both
2. **Purchase Timestamp Consistency**: `purchased_at` is automatically managed based on `is_purchased` status
3. **Cascade Deletion**: Deleting a shopping list removes all its items
4. **Referential Integrity**: Product variants and units are referenced with proper foreign keys

## Domain Models

### ShoppingList Entity

```go
type ShoppingList struct {
    ID          uuid.UUID          `json:"id" gorm:"type:uuid;primary_key"`
    PantryID    uuid.UUID          `json:"pantry_id" gorm:"type:uuid;not null"`
    Name        string             `json:"name" gorm:"type:varchar(255);not null"`
    Description *string            `json:"description,omitempty" gorm:"type:text"`
    CreatedBy   uuid.UUID          `json:"created_by" gorm:"type:uuid;not null"`
    Status      ShoppingListStatus `json:"status" gorm:"type:varchar(20);not null;default:'active'"`
    CreatedAt   time.Time          `json:"created_at" gorm:"type:timestamp with time zone;not null"`
    UpdatedAt   time.Time          `json:"updated_at" gorm:"type:timestamp with time zone;not null"`
    
    // Relationships
    Items []ShoppingListItemEntity `json:"items,omitempty" gorm:"foreignKey:ShoppingListID"`
    
    // Domain events
    events []DomainEvent `json:"-" gorm:"-"`
}
```

### ShoppingListItemEntity

```go
type ShoppingListItemEntity struct {
    ID               uuid.UUID  `json:"id" gorm:"type:uuid;primary_key"`
    ShoppingListID   uuid.UUID  `json:"shopping_list_id" gorm:"type:uuid;not null"`
    ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty" gorm:"type:uuid"`
    FreeTextName     *string    `json:"free_text_name,omitempty" gorm:"type:varchar(255)"`
    QuantityDesired  float64    `json:"quantity_desired" gorm:"type:decimal(10,3);not null"`
    UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty" gorm:"type:uuid"`
    Notes            *string    `json:"notes,omitempty" gorm:"type:text"`
    IsPurchased      bool       `json:"is_purchased" gorm:"type:boolean;not null;default:false"`
    PurchasedAt      *time.Time `json:"purchased_at,omitempty" gorm:"type:timestamp with time zone"`
    CreatedAt        time.Time  `json:"created_at" gorm:"type:timestamp with time zone;not null"`
    UpdatedAt        time.Time  `json:"updated_at" gorm:"type:timestamp with time zone;not null"`
    
    // Domain events
    events []DomainEvent `json:"-" gorm:"-"`
}
```

## Business Logic

### Core Operations

1. **Create Shopping List**: Validate pantry access and create new list
2. **Add Items**: Support both product variants and free-text items
3. **Update Items**: Modify quantities, notes, and other details
4. **Purchase Tracking**: Mark items as purchased/not purchased
5. **Status Management**: Transition between active, completed, and archived states
6. **Statistics**: Calculate completion percentages and item counts

### Validation Rules

1. **Pantry Access**: Users must be members of the pantry to access shopping lists
2. **Item Reference**: Each item must have either product variant ID or free text name
3. **Positive Quantities**: All quantities must be greater than zero
4. **Status Transitions**: Valid transitions between shopping list statuses
5. **Purchase Consistency**: Purchase timestamps are automatically managed

### Authorization

The shopping list service integrates with the pantry authorization system:

```go
// Check if user has access to the pantry
hasAccess, err := s.authService.IsMember(userID, shoppingList.PantryID)
if err != nil {
    return nil, fmt.Errorf("failed to check pantry access: %w", err)
}
if !hasAccess {
    return nil, fmt.Errorf("user does not have access to this shopping list")
}
```

## Repository Implementation

### GORM Repository

The shopping list repository is implemented using GORM with proper context handling:

```go
type ShoppingListRepository struct {
    db *gorm.DB
}

func (r *ShoppingListRepository) Create(ctx context.Context, shoppingList *domain.ShoppingList) error {
    return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
        // Create the shopping list
        if err := tx.Create(shoppingList).Error; err != nil {
            return err
        }
        
        // Create items if any
        for i := range shoppingList.Items {
            shoppingList.Items[i].ShoppingListID = shoppingList.ID
            if err := tx.Create(&shoppingList.Items[i]).Error; err != nil {
                return err
            }
        }
        
        return nil
    })
}
```

### Key Features

1. **Transaction Safety**: All operations use database transactions
2. **Context Support**: Proper context propagation for cancellation
3. **Bulk Operations**: Efficient batch updates for purchase status
4. **Statistics**: Aggregated queries for completion tracking
5. **Filtering**: Support for status, creator, and pagination filters

## Service Layer

### ShoppingListService

The service layer orchestrates business logic and enforces authorization:

```go
type ShoppingListService struct {
    shoppingListRepo domain.ShoppingListRepository
    pantryRepo       domain.PantryRepository
    userRepo         domain.UserRepository
    authService      domain.PantryAuthorizationService
}
```

### Key Responsibilities

1. **Authorization**: Validate user access to pantries and shopping lists
2. **Business Logic**: Enforce business rules and validation
3. **Coordination**: Orchestrate repository operations
4. **Error Handling**: Provide meaningful error messages
5. **Event Handling**: Dispatch domain events for audit trails

## Testing Strategy

### Unit Tests

1. **Domain Logic**: Test entity methods and business rules
2. **Validation**: Test request validation and constraints
3. **Authorization**: Test access control scenarios
4. **Repository**: Test database operations with mocks
5. **Service**: Test business logic orchestration

### Integration Tests

1. **Database**: Test repository implementations with real database
2. **Transactions**: Test transaction rollback scenarios
3. **Constraints**: Test database constraint enforcement
4. **Performance**: Test bulk operations and query performance

## Performance Considerations

### Database Optimization

1. **Indexes**: Proper indexes on foreign keys and query columns
2. **Pagination**: Efficient pagination for large shopping lists
3. **Bulk Operations**: Batch updates for purchase status changes
4. **Query Optimization**: Optimized queries for statistics and filtering

### Caching Strategy

1. **Shopping List Metadata**: Cache frequently accessed shopping list details
2. **Statistics**: Cache completion statistics with TTL
3. **User Permissions**: Cache pantry membership for authorization
4. **Product References**: Cache product variant details for display

## Future Enhancements

### Planned Features

1. **Recipe Integration**: Generate shopping lists from recipes
2. **Smart Suggestions**: AI-powered item recommendations
3. **Inventory Integration**: Auto-check against current inventory
4. **Sharing**: Share shopping lists between pantry members
5. **Templates**: Reusable shopping list templates
6. **Mobile Optimization**: Offline support and sync

### Technical Improvements

1. **Event Sourcing**: Full audit trail of shopping list changes
2. **CQRS**: Separate read and write models for performance
3. **Real-time Updates**: WebSocket support for collaborative shopping
4. **Advanced Analytics**: Shopping pattern analysis and insights
5. **Integration APIs**: Third-party grocery service integration

## Migration Guide

### Database Migration

The shopping list tables are created using migration `000006_create_shopping_list_tables.up.sql`:

```bash
# Apply the migration
make migrate-up

# Verify the migration
psql -d pantrypal -c "\dt shopping*"
```

### Code Integration

1. **Repository Registration**: Register the shopping list repository in dependency injection
2. **Service Registration**: Register the shopping list service with proper dependencies
3. **Handler Integration**: Create HTTP handlers for REST API endpoints
4. **Route Registration**: Add shopping list routes to the HTTP server
5. **Authorization**: Ensure proper middleware for authentication and authorization

## Troubleshooting

### Common Issues

1. **Foreign Key Violations**: Ensure product variants and units exist before referencing
2. **Authorization Errors**: Verify user membership in pantries
3. **Constraint Violations**: Check item reference validation (product variant XOR free text)
4. **Transaction Deadlocks**: Use proper transaction isolation levels
5. **Performance Issues**: Monitor query performance and add indexes as needed

### Debugging Tips

1. **Enable SQL Logging**: Use GORM's logger to debug SQL queries
2. **Check Constraints**: Verify database constraint violations
3. **Authorization Tracing**: Log authorization decisions for debugging
4. **Event Logging**: Monitor domain events for audit trails
5. **Performance Profiling**: Use database query analysis tools

This implementation provides a solid foundation for shopping list management with proper separation of concerns, comprehensive validation, and extensible architecture for future enhancements.
