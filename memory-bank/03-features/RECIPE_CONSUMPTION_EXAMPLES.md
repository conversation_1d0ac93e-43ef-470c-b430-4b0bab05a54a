# Recipe Ingredient Consumption Examples

This document provides examples of how to use the Recipe Ingredient Consumption feature in the Pantry Pal API.

## Overview

The Recipe Ingredient Consumption feature allows you to consume multiple ingredients from your pantry inventory for cooking recipes. It includes:

- **Smart Item Selection**: Automatically selects items based on expiration dates (FIFO)
- **Unit Conversion**: Converts between recipe units and inventory units
- **Partial Consumption**: Handles cases where you don't have enough of an ingredient
- **Detailed Reporting**: Provides comprehensive feedback on what was consumed

## API Endpoint

```
POST /api/v1/pantries/{pantryId}/inventory/recipe/consume
```

## Request Examples

### Example 1: Simple Recipe Consumption

```json
{
  "recipe_name": "Chocolate Chip Cookies",
  "ingredients": [
    {
      "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
      "required_quantity": 2.0,
      "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440010",
      "allow_partial": false
    },
    {
      "product_variant_id": "550e8400-e29b-41d4-a716-446655440002",
      "required_quantity": 1.5,
      "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440011",
      "allow_partial": true
    }
  ],
  "auto_select": true,
  "allow_partial": true,
  "notes": "Making cookies for the office party"
}
```

### Example 2: Recipe with Preferred Items

```json
{
  "recipe_id": "550e8400-e29b-41d4-a716-446655440100",
  "recipe_name": "Beef Stir Fry",
  "serving_size": 4,
  "ingredients": [
    {
      "product_variant_id": "550e8400-e29b-41d4-a716-446655440003",
      "required_quantity": 500,
      "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440012",
      "allow_partial": false,
      "preferred_items": [
        "550e8400-e29b-41d4-a716-446655440201",
        "550e8400-e29b-41d4-a716-446655440202"
      ],
      "notes": "Use the fresh beef from the fridge"
    },
    {
      "product_variant_id": "550e8400-e29b-41d4-a716-446655440004",
      "required_quantity": 2,
      "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440013",
      "allow_partial": true
    }
  ],
  "auto_select": true,
  "allow_partial": false,
  "notes": "Dinner for the family"
}
```

## Response Examples

### Successful Consumption Response

```json
{
  "success": true,
  "message": "Recipe ingredients consumed successfully",
  "data": {
    "recipe_name": "Chocolate Chip Cookies",
    "success_count": 2,
    "failure_count": 0,
    "partial_count": 0,
    "total_ingredients": 2,
    "can_proceed": true,
    "results": [
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
        "required_quantity": 2.0,
        "consumed_quantity": 2.0,
        "remaining_quantity": 0.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440010",
        "success": true,
        "partial": false,
        "items_consumed": [
          {
            "item_id": "550e8400-e29b-41d4-a716-446655440301",
            "consumed_quantity": 2.0,
            "remaining_quantity": 3.0,
            "location_id": "550e8400-e29b-41d4-a716-446655440401",
            "expiration_date": "2024-12-31",
            "purchase_price": 4.99
          }
        ]
      },
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440002",
        "required_quantity": 1.5,
        "consumed_quantity": 1.5,
        "remaining_quantity": 0.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440011",
        "success": true,
        "partial": false,
        "items_consumed": [
          {
            "item_id": "550e8400-e29b-41d4-a716-446655440302",
            "consumed_quantity": 1.5,
            "remaining_quantity": 0.5,
            "location_id": "550e8400-e29b-41d4-a716-446655440402",
            "expiration_date": "2024-11-15"
          }
        ]
      }
    ],
    "summary": {
      "total_ingredients": 2,
      "successful_ingredients": 2,
      "partial_ingredients": 0,
      "failed_ingredients": 0,
      "completion_rate": 100.0,
      "can_proceed_with_recipe": true,
      "total_value": 7.48,
      "total_items_consumed": 2
    }
  }
}
```

### Partial Consumption Response

```json
{
  "success": true,
  "message": "Recipe ingredients consumed successfully",
  "data": {
    "recipe_name": "Beef Stir Fry",
    "success_count": 1,
    "failure_count": 0,
    "partial_count": 1,
    "total_ingredients": 2,
    "can_proceed": true,
    "results": [
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440003",
        "required_quantity": 500,
        "consumed_quantity": 500,
        "remaining_quantity": 0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440012",
        "success": true,
        "partial": false,
        "items_consumed": [
          {
            "item_id": "550e8400-e29b-41d4-a716-446655440201",
            "consumed_quantity": 500,
            "remaining_quantity": 0,
            "location_id": "550e8400-e29b-41d4-a716-446655440401",
            "expiration_date": "2024-11-20",
            "purchase_price": 12.99
          }
        ]
      },
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440004",
        "required_quantity": 2.0,
        "consumed_quantity": 1.5,
        "remaining_quantity": 0.5,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440013",
        "success": false,
        "partial": true,
        "items_consumed": [
          {
            "item_id": "550e8400-e29b-41d4-a716-446655440303",
            "consumed_quantity": 1.5,
            "remaining_quantity": 0,
            "location_id": "550e8400-e29b-41d4-a716-446655440403",
            "expiration_date": "2024-11-25"
          }
        ]
      }
    ],
    "summary": {
      "total_ingredients": 2,
      "successful_ingredients": 1,
      "partial_ingredients": 1,
      "failed_ingredients": 0,
      "completion_rate": 75.0,
      "can_proceed_with_recipe": true,
      "total_value": 12.99,
      "total_items_consumed": 2
    }
  }
}
```

## Key Features

### 1. Smart Item Selection
- **Auto-select**: When enabled, automatically selects items based on expiration dates (FIFO)
- **Preferred Items**: Specify specific inventory items to use first
- **Expiration Priority**: Items expiring sooner are consumed first

### 2. Unit Conversion
- Automatically converts between recipe units and inventory units
- Supports all compatible unit types (weight, volume, count)
- Handles conversion errors gracefully

### 3. Partial Consumption
- **Allow Partial**: Control whether partial consumption is allowed per ingredient
- **Detailed Reporting**: Shows exactly how much was consumed vs. required
- **Recipe Viability**: Indicates whether the recipe can still proceed

### 4. Comprehensive Reporting
- **Success/Failure Tracking**: Per-ingredient status
- **Item Details**: Shows which specific inventory items were consumed
- **Value Tracking**: Calculates total value of consumed ingredients
- **Completion Rate**: Percentage of ingredients successfully obtained

## Use Cases

1. **Home Cooking**: Consume ingredients for family meals
2. **Restaurant Operations**: Track ingredient usage for menu items
3. **Meal Planning**: Check ingredient availability before cooking
4. **Inventory Management**: Automatic FIFO rotation of perishables
5. **Cost Tracking**: Monitor the cost of recipes and meals

## Error Handling

The system gracefully handles various error scenarios:
- Insufficient stock (with partial consumption options)
- Unit conversion errors
- Missing ingredients
- Permission errors
- Invalid product variants

Each error provides detailed information to help users understand what went wrong and how to resolve it.
