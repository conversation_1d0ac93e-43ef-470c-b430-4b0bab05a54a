# User & Pantry Management

## Overview
This document details the requirements for User and Pantry Management features in Pantry Pal, including user authentication, profile management, pantry creation, and collaborative pantry sharing with role-based access control.

## Key Features & Requirements

### User Management
- **Secure Registration**: As a user, I can securely register a new account by providing a unique email and username, and a strong password that adheres to defined policies (e.g., minimum 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character).
- **User Login**: As a user, I can log in to my account using my registered email/username and password.
- **Profile Management**: As a user, I can manage my profile information including first name, last name, and profile picture.
- **Password Reset**: As a user, I can initiate a password reset process if I forget my password, involving email verification.

### Pantry Management
- **Multiple Pantries**: As a user, I can create and manage multiple distinct pantries (e.g., "Kitchen Pantry," "Garage Storage") to organize my items.
- **Pantry Editing**: As a user, I can edit pantry details such as name and description.
- **Pantry Deletion**: As a user, I can delete a pantry I own (if no other members, or after ownership transfer).

### Pantry Sharing & Collaboration
- **Invite Members**: As a pantry owner, I can invite other users (family/friends) to my pantry via their email or username, allowing collaborative management.
- **Accept/Reject Invitation**: As an invited user, I can accept or reject a pantry invitation.
- **Role-Based Access Control (RBAC)**: As a pantry owner/admin, I can assign specific roles (Owner, Admin, Editor, Viewer) to pantry members to control their permissions.
    - **Permissions**:
        - **Owner**: Full control, can delete pantry, invite/remove any role, transfer ownership.
        - **Admin**: Add/remove items, manage locations, invite/remove Editors/Viewers.
        - **Editor**: Add/remove items, manage locations.
        - **Viewer**: Read-only access.
- **Transfer Ownership**: As a pantry owner, I can transfer ownership of a pantry to another member.
- **Remove Members**: As a pantry owner/admin, I can remove members from a pantry.
- **Leave Pantry**: As a user, I can leave a pantry I am a member of.

## Related Entities
- [`User`](internal/core/domain/user.go)
- [`Pantry`](internal/core/domain/pantry.go)
- [`PantryMembership`](internal/core/domain/pantry_membership.go)
- [`RefreshToken`](internal/core/domain/auth.go)

## API Endpoints (Conceptual)
- `POST /auth/register`
- `POST /auth/login`
- `POST /auth/refresh`
- `POST /auth/logout`
- `GET /users/profile`
- `PUT /users/profile`
- `POST /users/change-password`
- `POST /pantries`
- `GET /pantries`
- `GET /pantries/{pantryId}`
- `PUT /pantries/{pantryId}`
- `DELETE /pantries/{pantryId}`
- `POST /pantries/{pantryId}/memberships` (Invite member)
- `PATCH /pantries/{pantryId}/memberships/{membershipId}/role` (Change role)
- `DELETE /pantries/{pantryId}/memberships/{membershipId}` (Remove member)
- `POST /pantries/{pantryId}/transfer-ownership`

## Implementation Notes
- Password hashing using bcrypt.
- JWT for authentication with refresh tokens.
- Casbin for fine-grained authorization (future migration).
- Multi-tenancy enforced by `pantry_id` scoping.