# Idempotency Middleware Examples

This document provides examples of how to use the Idempotency Middleware in the Pantry Pal API for production reliability.

## Overview

The Idempotency Middleware provides protection against duplicate operations caused by:

- **Network Retries**: Client retries due to network timeouts
- **Concurrent Requests**: Multiple identical requests from the same client
- **User Interface Issues**: Double-clicks or multiple form submissions
- **Integration Failures**: Webhook retries and API client retries

## Key Features

- **Redis-Based Storage**: Distributed idempotency key storage with TTL
- **Request Fingerprinting**: SHA256 hashing of request data for comparison
- **Response Caching**: Cached responses for completed requests
- **Configurable TTL**: Customizable expiration times for idempotency keys
- **Status Tracking**: Pending, completed, failed, and expired status management

## Configuration

### Default Configuration

```go
config := domain.DefaultIdempotencyConfig()
// Enabled: true
// DefaultTTL: 24 hours
// MaxTTL: 7 days
// KeyHeader: "Idempotency-Key"
// IncludeUserID: true
// IncludeRequestBody: true
// ExcludedPaths: ["/health", "/metrics", "/ping"]
// ExcludedMethods: ["GET", "HEAD", "OPTIONS"]
// MaxRequestSize: 1MB
// MaxResponseSize: 1MB
```

### Custom Configuration

```go
config := &domain.IdempotencyConfig{
    Enabled:           true,
    DefaultTTL:        12 * time.Hour,
    MaxTTL:            3 * 24 * time.Hour,
    KeyHeader:         "X-Idempotency-Key",
    IncludeUserID:     true,
    IncludeRequestBody: true,
    ExcludedPaths: []string{
        "/health",
        "/metrics",
        "/api/v1/auth/login",
    },
    ExcludedMethods: []string{
        "GET", "HEAD", "OPTIONS",
    },
    MaxRequestSize:  512 * 1024,  // 512KB
    MaxResponseSize: 512 * 1024,  // 512KB
}
```

## Usage Examples

### Example 1: Basic Idempotent Request

```bash
# First request - processes normally
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-************" \
  -H "Content-Type: application/json" \
  -d '{
    "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
    "quantity": 5.0,
    "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440002"
  }'

# Response: 201 Created
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440003",
    "quantity": 5.0,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

```bash
# Duplicate request - returns cached response
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-************" \
  -H "Content-Type: application/json" \
  -d '{
    "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
    "quantity": 5.0,
    "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440002"
  }'

# Response: 201 Created (cached)
# Headers: X-Idempotent-Replayed: true
#          X-Idempotent-Original-Time: 2024-01-15T10:30:00Z
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440003",
    "quantity": 5.0,
    "created_at": "2024-01-15T10:30:00Z"
  }
}
```

### Example 2: Concurrent Request Handling

```bash
# First request starts processing
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory/bulk" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-446655440001" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [/* large batch of items */]
  }'

# Concurrent request with same key - returns conflict
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory/bulk" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-446655440001" \
  -H "Content-Type: application/json" \
  -d '{
    "items": [/* same batch of items */]
  }'

# Response: 409 Conflict
{
  "error": "Request is already being processed",
  "retry_after": "1"
}
```

### Example 3: Request Data Mismatch

```bash
# Original request
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-446655440002" \
  -H "Content-Type: application/json" \
  -d '{
    "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
    "quantity": 5.0
  }'

# Different request with same idempotency key
curl -X POST "https://api.pantrypal.com/api/v1/pantries/123/inventory" \
  -H "Authorization: Bearer token" \
  -H "Idempotency-Key: 550e8400-e29b-41d4-a716-446655440002" \
  -H "Content-Type: application/json" \
  -d '{
    "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
    "quantity": 10.0
  }'

# Response: 409 Conflict
{
  "error": "Idempotency key conflict: Idempotency key exists with different request data"
}
```

## Monitoring and Statistics

### Get Idempotency Statistics

```bash
curl -X GET "https://api.pantrypal.com/api/v1/admin/idempotency/stats" \
  -H "Authorization: Bearer admin_token"

# Response:
{
  "success": true,
  "data": {
    "enabled": true,
    "config": {
      "default_ttl": "24h0m0s",
      "max_ttl": "168h0m0s",
      "key_header": "Idempotency-Key",
      "include_user_id": true,
      "include_request_body": true
    },
    "repository": {
      "total_keys": 1247,
      "prefix": "idempotency",
      "status_counts": {
        "completed": 1156,
        "pending": 3,
        "failed": 88
      }
    }
  }
}
```

### Cleanup Expired Keys

```bash
curl -X POST "https://api.pantrypal.com/api/v1/admin/idempotency/cleanup" \
  -H "Authorization: Bearer admin_token"

# Response:
{
  "success": true,
  "message": "Cleanup completed successfully"
}
```

## Implementation Details

### Request Fingerprinting

The middleware creates a unique fingerprint for each request using:

1. **HTTP Method**: POST, PUT, PATCH, DELETE
2. **Request Path**: /api/v1/pantries/123/inventory
3. **User ID**: Extracted from authentication context
4. **Request Body**: JSON payload (if enabled)

```go
// Example fingerprint generation
fingerprint := SHA256(
    "POST" + "|" +
    "/api/v1/pantries/123/inventory" + "|" +
    "user-uuid" + "|" +
    '{"product_variant_id":"...","quantity":5.0}'
)
```

### Status Lifecycle

1. **Pending**: Request is being processed
2. **Completed**: Request completed successfully, response cached
3. **Failed**: Request failed, can be retried
4. **Expired**: Key expired, treated as new request

### Redis Storage

```redis
# Key format: idempotency:{uuid}
# Value: JSON-encoded IdempotencyKey struct
# TTL: Configurable (default 24 hours)

SET idempotency:550e8400-e29b-41d4-a716-************ '{
  "key": "550e8400-e29b-41d4-a716-************",
  "request_hash": "abc123...",
  "method": "POST",
  "path": "/api/v1/pantries/123/inventory",
  "status": "completed",
  "response_code": 201,
  "response_body": "{\"success\":true,...}",
  "created_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-01-16T10:30:00Z"
}' EX 86400
```

## Best Practices

### Client Implementation

1. **Generate UUID Keys**: Use UUID v4 for idempotency keys
2. **Retry Logic**: Implement exponential backoff for 409 responses
3. **Key Management**: Store keys locally to handle retries
4. **Error Handling**: Handle different response codes appropriately

```javascript
// Example client implementation
async function createInventoryItem(data) {
  const idempotencyKey = uuidv4();
  
  try {
    const response = await fetch('/api/v1/pantries/123/inventory', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Idempotency-Key': idempotencyKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    
    if (response.status === 409) {
      // Request is being processed or conflict
      const error = await response.json();
      if (error.retry_after) {
        // Wait and retry
        await sleep(parseInt(error.retry_after) * 1000);
        return createInventoryItem(data);
      }
      throw new Error(error.error);
    }
    
    return await response.json();
  } catch (error) {
    // Handle network errors with retry
    throw error;
  }
}
```

### Server Configuration

1. **Appropriate TTL**: Set TTL based on business requirements
2. **Size Limits**: Configure request/response size limits
3. **Path Exclusions**: Exclude read-only and health check endpoints
4. **Monitoring**: Monitor idempotency key usage and conflicts

## Production Considerations

### Performance

- **Redis Performance**: Ensure Redis cluster can handle the load
- **Memory Usage**: Monitor Redis memory usage for idempotency keys
- **Network Overhead**: Consider request/response size limits

### Security

- **Key Validation**: Validate idempotency key format (UUID)
- **User Isolation**: Include user ID in fingerprinting
- **Rate Limiting**: Combine with rate limiting middleware

### Reliability

- **Redis Availability**: Use Redis cluster for high availability
- **Graceful Degradation**: Handle Redis failures gracefully
- **Monitoring**: Monitor idempotency conflicts and failures

## Troubleshooting

### Common Issues

1. **High Conflict Rate**: Check for client-side key generation issues
2. **Memory Growth**: Monitor and cleanup expired keys
3. **Performance Impact**: Optimize Redis configuration
4. **False Conflicts**: Verify request fingerprinting logic

### Debugging

```bash
# Check specific idempotency key
redis-cli GET "idempotency:550e8400-e29b-41d4-a716-************"

# List all idempotency keys
redis-cli KEYS "idempotency:*"

# Monitor idempotency operations
redis-cli MONITOR | grep idempotency
```
