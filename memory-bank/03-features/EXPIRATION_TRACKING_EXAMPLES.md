# Expiration Tracking & Alerts Examples

This document provides examples of how to use the Expiration Tracking & Alerts feature in the Pantry Pal API.

## Overview

The Expiration Tracking & Alerts feature provides intelligent monitoring of item expiration dates with:

- **Smart Status Classification**: Warning, Alert, Critical, and Expired status levels
- **Flexible Notification System**: Multi-channel notifications (Email, Telegram, Supabase, Webhook)
- **Configurable Thresholds**: Customizable warning periods for different urgency levels
- **Actionable Recommendations**: Suggested actions for each expiration status
- **Comprehensive Analytics**: Value tracking and category breakdown

## API Endpoints

### Expiration Tracking
```
POST /api/v1/pantries/{pantryId}/expiration/track
```

### Alert Configuration
```
POST /api/v1/pantries/{pantryId}/expiration/alerts
GET /api/v1/pantries/{pantryId}/expiration/alerts
POST /api/v1/expiration/alerts/global
GET /api/v1/expiration/alerts/global
```

## Request Examples

### Example 1: Basic Expiration Tracking

```json
{
  "warning_days": 7,
  "alert_days": 3,
  "critical_days": 1,
  "send_alerts": false
}
```

### Example 2: Expiration Tracking with Notifications

```json
{
  "warning_days": 10,
  "alert_days": 5,
  "critical_days": 2,
  "channels": ["email", "telegram"],
  "send_alerts": true
}
```

### Example 3: Category-Specific Tracking

```json
{
  "warning_days": 7,
  "alert_days": 3,
  "critical_days": 1,
  "category_ids": [
    "550e8400-e29b-41d4-a716-446655440001",
    "550e8400-e29b-41d4-a716-446655440002"
  ],
  "send_alerts": true,
  "channels": ["email"]
}
```

### Example 4: Alert Configuration

```json
{
  "enabled": true,
  "warning_days": 7,
  "alert_days": 3,
  "critical_days": 1,
  "channels": ["email", "telegram", "supabase"],
  "quiet_hours": {
    "enabled": true,
    "start_time": "22:00",
    "end_time": "08:00",
    "timezone": "America/New_York"
  },
  "min_value": 5.00
}
```

## Response Examples

### Successful Expiration Tracking Response

```json
{
  "success": true,
  "message": "Expiring items tracked successfully",
  "data": {
    "total_items": 12,
    "warning_items": 5,
    "alert_items": 4,
    "critical_items": 2,
    "expired_items": 1,
    "alerts_sent": 8,
    "checked_at": "2024-01-15T10:30:00Z",
    "items": [
      {
        "item_id": "550e8400-e29b-41d4-a716-446655440001",
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440101",
        "product_name": "Organic Milk",
        "variant_name": "1 Liter",
        "category_id": "550e8400-e29b-41d4-a716-446655440201",
        "category_name": "Dairy",
        "quantity": 2.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440301",
        "unit_name": "pieces",
        "unit_symbol": "pcs",
        "expiration_date": "2024-01-17",
        "days_until_expiry": 2,
        "expiration_status": "critical",
        "location_id": "550e8400-e29b-41d4-a716-446655440401",
        "location_name": "Main Fridge",
        "purchase_price": 7.98,
        "estimated_value": 7.98,
        "actions": [
          {
            "type": "consume",
            "description": "Use immediately or today",
            "priority": 1
          },
          {
            "type": "freeze",
            "description": "Freeze if possible to extend life",
            "priority": 2
          }
        ]
      },
      {
        "item_id": "550e8400-e29b-41d4-a716-446655440002",
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440102",
        "product_name": "Greek Yogurt",
        "variant_name": "500g Plain",
        "category_id": "550e8400-e29b-41d4-a716-446655440201",
        "category_name": "Dairy",
        "quantity": 1.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440301",
        "unit_name": "pieces",
        "unit_symbol": "pcs",
        "expiration_date": "2024-01-14",
        "days_until_expiry": -1,
        "expiration_status": "expired",
        "estimated_value": 5.99,
        "actions": [
          {
            "type": "discard",
            "description": "Remove expired item immediately",
            "priority": 1
          },
          {
            "type": "check",
            "description": "Inspect for safety before disposal",
            "priority": 2
          }
        ]
      }
    ],
    "summary": {
      "total_value": 47.85,
      "warning_value": 18.50,
      "alert_value": 15.47,
      "critical_value": 7.98,
      "expired_value": 5.99,
      "top_categories": [
        {
          "category_id": "550e8400-e29b-41d4-a716-446655440201",
          "category_name": "Dairy",
          "item_count": 6,
          "total_value": 28.47,
          "worst_status": "expired"
        },
        {
          "category_id": "550e8400-e29b-41d4-a716-446655440202",
          "category_name": "Produce",
          "item_count": 4,
          "total_value": 12.50,
          "worst_status": "critical"
        }
      ],
      "recommended_actions": [
        "Remove expired items immediately",
        "Use critical items today",
        "Plan meals with expiring items"
      ]
    }
  }
}
```

### Alert Configuration Response

```json
{
  "success": true,
  "message": "Alert configuration saved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440500",
    "user_id": "550e8400-e29b-41d4-a716-446655440600",
    "pantry_id": "550e8400-e29b-41d4-a716-446655440700",
    "enabled": true,
    "warning_days": 7,
    "alert_days": 3,
    "critical_days": 1,
    "channels": ["email", "telegram"],
    "quiet_hours": {
      "enabled": true,
      "start_time": "22:00",
      "end_time": "08:00",
      "timezone": "America/New_York"
    },
    "min_value": 5.00,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

## Key Features

### 1. Smart Status Classification
- **Warning** (7 days): Items approaching expiration
- **Alert** (3 days): Items expiring soon
- **Critical** (1 day): Items expiring very soon
- **Expired**: Items past expiration date

### 2. Multi-Channel Notifications
- **Email**: Traditional email notifications
- **Telegram**: Instant messaging alerts
- **Supabase**: Real-time in-app notifications
- **Webhook**: Custom integrations
- **In-App**: Dashboard notifications

### 3. Intelligent Recommendations
- **Consume**: Use items in meals
- **Freeze**: Extend shelf life
- **Donate**: Give away before expiration
- **Discard**: Remove expired items
- **Check**: Inspect for safety

### 4. Advanced Configuration
- **Quiet Hours**: No notifications during sleep hours
- **Minimum Value**: Only alert for valuable items
- **Category Filters**: Focus on specific categories
- **Custom Thresholds**: Adjust warning periods

## Notification Adapter System

### Supported Providers
1. **Email Provider**: SMTP-based email notifications
2. **Telegram Provider**: Bot-based messaging
3. **Supabase Provider**: Real-time notifications
4. **Webhook Provider**: Custom HTTP callbacks

### Provider Configuration
Each provider can be enabled/disabled and configured independently:

```go
// Email Provider
emailProvider := providers.NewEmailProvider(
    "smtp.gmail.com", 587,
    "username", "password",
    "<EMAIL>", "Pantry Pal",
    true, logger,
)

// Telegram Provider
telegramProvider := providers.NewTelegramProvider(
    "bot_token", true, logger,
)

// Register providers
notificationService.RegisterProvider(emailProvider)
notificationService.RegisterProvider(telegramProvider)
```

## Use Cases

### 1. Home Kitchen Management
- Daily expiration checks
- Email alerts for family members
- Smart meal planning suggestions

### 2. Restaurant Operations
- Critical item monitoring
- Staff notifications via Telegram
- Waste reduction tracking

### 3. Food Banks & Charities
- Donation opportunity alerts
- Value-based prioritization
- Category-specific monitoring

### 4. Meal Prep Services
- Batch expiration tracking
- Automated reordering triggers
- Quality control alerts

## Integration Examples

### Daily Automated Checks
```bash
# Cron job for daily expiration checks
0 8 * * * curl -X POST "https://api.pantrypal.com/expiration/alerts/process"
```

### Webhook Integration
```json
{
  "webhook_url": "https://your-app.com/webhooks/expiration",
  "events": ["expiration_warning", "expiration_critical"],
  "secret": "your_webhook_secret"
}
```

### Mobile App Integration
```javascript
// Subscribe to real-time notifications
const supabase = createClient(url, key)
supabase
  .channel('expiration_alerts')
  .on('postgres_changes', {
    event: 'INSERT',
    schema: 'public',
    table: 'notifications'
  }, (payload) => {
    showExpirationAlert(payload.new)
  })
  .subscribe()
```

## Future Enhancements

- **Machine Learning**: Predictive expiration modeling
- **Smart Suggestions**: Recipe recommendations for expiring items
- **Batch Processing**: Bulk expiration operations
- **Advanced Analytics**: Waste reduction insights
- **Mobile Push**: Native mobile notifications
