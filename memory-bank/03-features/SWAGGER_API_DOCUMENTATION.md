# Swagger/OpenAPI Documentation

This document provides information about the Swagger/OpenAPI documentation implementation for the Pantry Pal API.

## Overview

The Pantry Pal API includes comprehensive OpenAPI/Swagger documentation that provides:

- **Interactive API Documentation**: Browse and test API endpoints directly from the browser
- **Request/Response Examples**: Complete examples for all endpoints
- **Schema Definitions**: Detailed data models and validation rules
- **Authentication Documentation**: JWT Bearer token authentication
- **Error Response Documentation**: Standardized error responses

## Accessing the Documentation

### Swagger UI

The interactive Swagger UI is available at:
```
http://localhost:8080/docs/
```

### OpenAPI Specification Files

The OpenAPI specification is available in multiple formats:

- **JSON Format**: `docs/swagger.json`
- **YAML Format**: `docs/swagger.yaml`
- **Go Documentation**: `docs/docs.go`

## API Information

### Base Configuration

```yaml
title: Pantry Pal API
version: 2.0
description: A comprehensive multi-tenant pantry management system for tracking inventory, managing shopping lists, and reducing food waste.
host: localhost:8080
basePath: /api/v1
```

### Authentication

The API uses JWT Bearer token authentication:

```yaml
securityDefinitions:
  BearerAuth:
    type: apiKey
    name: Authorization
    in: header
    description: Type "Bearer" followed by a space and JWT token.
```

## Documented Endpoints

### Authentication Endpoints

#### POST /auth/register
- **Summary**: Register a new user
- **Description**: Create a new user account with email, username, and password
- **Request Body**: `RegisterCredentials`
- **Responses**: 201 (Created), 400 (Bad Request), 409 (Conflict), 500 (Internal Server Error)

#### POST /auth/login
- **Summary**: User login
- **Description**: Authenticate user with email and password
- **Request Body**: `LoginCredentials`
- **Responses**: 200 (OK), 400 (Bad Request), 401 (Unauthorized), 500 (Internal Server Error)

#### POST /auth/refresh
- **Summary**: Refresh access token
- **Description**: Generate new access token using refresh token from cookie
- **Responses**: 200 (OK), 401 (Unauthorized), 500 (Internal Server Error)

#### POST /auth/logout
- **Summary**: User logout
- **Description**: Logout user and revoke refresh token
- **Responses**: 200 (OK), 500 (Internal Server Error)

## Data Models

### Request Models

#### RegisterCredentials
```json
{
  "username": "string (3-50 chars, required)",
  "email": "string (email format, required)",
  "password": "string (min 8 chars, required)",
  "confirm_password": "string (required)",
  "first_name": "string (max 100 chars, optional)",
  "last_name": "string (max 100 chars, optional)"
}
```

#### LoginCredentials
```json
{
  "email": "string (email format, required)",
  "password": "string (min 8 chars, required)"
}
```

### Response Models

#### APIResponse
```json
{
  "success": "boolean",
  "data": "object (optional)",
  "error": "ErrorInfo (optional)",
  "message": "string (optional)",
  "metadata": "object (optional)",
  "timestamp": "string (ISO 8601)",
  "request_id": "string (optional)"
}
```

#### ErrorInfo
```json
{
  "code": "string",
  "message": "string",
  "details": "object (optional)"
}
```

## Development Workflow

### Generating Documentation

The Swagger documentation is generated using the `swag` tool:

```bash
# Install swag tool
go install github.com/swaggo/swag/cmd/swag@latest

# Generate documentation
swag init -g cmd/api/main.go -o docs
```

### Adding Swagger Annotations

To document a new endpoint, add Swagger annotations to the handler function:

```go
// CreateItem handles item creation
//
//	@Summary		Create a new item
//	@Description	Create a new inventory item in the specified pantry
//	@Tags			Inventory
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			pantryId	path		string				true	"Pantry ID"
//	@Param			request		body		CreateItemRequest	true	"Item data"
//	@Success		201			{object}	APIResponse			"Item created successfully"
//	@Failure		400			{object}	APIResponse			"Invalid input"
//	@Failure		401			{object}	APIResponse			"Unauthorized"
//	@Failure		403			{object}	APIResponse			"Forbidden"
//	@Failure		500			{object}	APIResponse			"Internal server error"
//	@Router			/pantries/{pantryId}/inventory [post]
func (h *InventoryHandler) CreateItem(c *fiber.Ctx) error {
    // Implementation
}
```

### Annotation Guidelines

#### Required Annotations
- `@Summary`: Brief description of the endpoint
- `@Description`: Detailed description of what the endpoint does
- `@Tags`: Group endpoints by functionality
- `@Accept`: Request content type (usually `json`)
- `@Produce`: Response content type (usually `json`)
- `@Router`: HTTP method and path

#### Optional Annotations
- `@Security`: Authentication requirements
- `@Param`: Request parameters (path, query, body)
- `@Success`: Successful response definitions
- `@Failure`: Error response definitions

#### Parameter Types
- `path`: URL path parameters
- `query`: Query string parameters
- `body`: Request body
- `header`: HTTP headers
- `formData`: Form data

## Tags and Organization

The API endpoints are organized using the following tags:

- **Authentication**: User authentication and session management
- **Users**: User profile management
- **Pantries**: Pantry creation and management
- **Pantry Memberships**: Pantry sharing and collaboration
- **Categories**: Product category management
- **Products**: Product catalog management
- **Product Variants**: Specific product variant management
- **Units of Measure**: Unit of measure and conversion management
- **Pantry Locations**: Storage location management within pantries
- **Inventory**: Inventory item tracking and management
- **Shopping Lists**: Shopping list creation and management
- **Expiration Tracking**: Food expiration monitoring and alerts
- **Admin**: Administrative endpoints and system management

## Best Practices

### Documentation Standards

1. **Consistent Naming**: Use consistent naming conventions for parameters and responses
2. **Clear Descriptions**: Provide clear, concise descriptions for all endpoints
3. **Complete Examples**: Include realistic examples in request/response schemas
4. **Error Documentation**: Document all possible error responses
5. **Security Documentation**: Clearly indicate authentication requirements

### Model Definitions

1. **Reusable Models**: Define reusable models for common data structures
2. **Validation Rules**: Include validation rules in model definitions
3. **Optional Fields**: Clearly mark optional fields
4. **Example Values**: Provide realistic example values

### Maintenance

1. **Automated Generation**: Use automated tools to generate documentation
2. **Version Control**: Keep documentation in version control
3. **Regular Updates**: Update documentation with code changes
4. **Review Process**: Include documentation review in code review process

## Integration Examples

### Frontend Integration

```javascript
// Using the OpenAPI specification with code generation
import { PantryPalApi } from './generated/api';

const api = new PantryPalApi({
  basePath: 'http://localhost:8080/api/v1',
  accessToken: 'your-jwt-token'
});

// Register a new user
const registerResponse = await api.authRegisterPost({
  username: 'johndoe',
  email: '<EMAIL>',
  password: 'securepassword',
  confirm_password: 'securepassword'
});
```

### Testing Integration

```bash
# Using curl with Swagger documentation
curl -X POST "http://localhost:8080/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "johndoe",
    "email": "<EMAIL>",
    "password": "securepassword",
    "confirm_password": "securepassword"
  }'
```

### Postman Integration

1. Import the OpenAPI specification into Postman
2. Use the generated collection for API testing
3. Leverage environment variables for different environments

## Future Enhancements

### Planned Improvements

1. **Complete Endpoint Coverage**: Document all remaining endpoints
2. **Advanced Examples**: Add more complex request/response examples
3. **Environment Configuration**: Support for different environments (dev, staging, prod)
4. **Custom Themes**: Custom Swagger UI themes
5. **API Versioning**: Support for multiple API versions

### Additional Features

1. **Mock Server**: Generate mock servers from OpenAPI specification
2. **Client Generation**: Generate client SDKs for different languages
3. **Validation**: Runtime request/response validation
4. **Monitoring**: API usage monitoring and analytics

## Troubleshooting

### Common Issues

1. **Missing Endpoints**: Ensure Swagger annotations are properly formatted
2. **Type Errors**: Verify that referenced types exist and are importable
3. **Generation Failures**: Check for syntax errors in annotations
4. **UI Not Loading**: Verify that the docs directory is properly served

### Debug Commands

```bash
# Validate OpenAPI specification
swagger-codegen validate -i docs/swagger.json

# Generate client code for testing
swagger-codegen generate -i docs/swagger.json -l javascript -o client/

# Check for annotation errors
swag init -g cmd/api/main.go -o docs --parseInternal
```

## Conclusion

The Swagger/OpenAPI documentation provides a comprehensive, interactive way to explore and test the Pantry Pal API. It serves as both documentation and a testing tool, making it easier for developers to integrate with the API and understand its capabilities.

The documentation is automatically generated from code annotations, ensuring it stays up-to-date with the actual implementation. This approach provides a single source of truth for API documentation and reduces the maintenance burden.
