# Shopping List Feature Documentation

## Overview

The Shopping List feature allows users to create and manage shopping lists within their pantries. Users can add items either by referencing existing product variants from the catalog or by entering free-text items. The system tracks purchase status and provides completion statistics.

## Architecture

### Domain Layer

#### Entities

**ShoppingList**
- Represents a shopping list within a pantry
- Contains metadata like name, description, status, and creation info
- Supports three statuses: `active`, `completed`, `archived`
- Tracks creation and modification timestamps

**ShoppingListItemEntity**
- Represents individual items in a shopping list
- Supports two types of items:
  - **Product Variant Items**: References existing products from the catalog
  - **Free Text Items**: Custom text entries for items not in the catalog
- Tracks quantity, unit of measure, notes, and purchase status
- Automatically manages purchase timestamps

#### Business Rules

1. **Item Reference Validation**: Each item must have either a product variant ID or free text name, but not both
2. **Pantry Access Control**: Users can only access shopping lists in pantries they belong to
3. **Quantity Validation**: All quantities must be positive numbers
4. **Status Transitions**: Shopping lists can transition between active, completed, and archived states
5. **Purchase Tracking**: Items automatically get timestamped when marked as purchased

### Repository Layer

**ShoppingListRepository Interface**
```go
type ShoppingListRepository interface {
    // Shopping List operations
    Create(ctx context.Context, shoppingList *ShoppingList) error
    GetByID(ctx context.Context, id uuid.UUID) (*ShoppingList, error)
    GetByPantryID(ctx context.Context, pantryID uuid.UUID, filters ShoppingListFilters) ([]*ShoppingList, error)
    Update(ctx context.Context, shoppingList *ShoppingList) error
    Delete(ctx context.Context, id uuid.UUID) error

    // Shopping List Item operations
    AddItem(ctx context.Context, item *ShoppingListItemEntity) error
    UpdateItem(ctx context.Context, item *ShoppingListItemEntity) error
    RemoveItem(ctx context.Context, itemID uuid.UUID) error
    GetItemByID(ctx context.Context, itemID uuid.UUID) (*ShoppingListItemEntity, error)
    GetItemsByShoppingListID(ctx context.Context, shoppingListID uuid.UUID) ([]*ShoppingListItemEntity, error)

    // Bulk operations
    MarkItemsAsPurchased(ctx context.Context, itemIDs []uuid.UUID) error
    MarkItemsAsNotPurchased(ctx context.Context, itemIDs []uuid.UUID) error

    // Statistics and analytics
    GetShoppingListStats(ctx context.Context, shoppingListID uuid.UUID) (*ShoppingListStats, error)
    GetPantryShoppingListStats(ctx context.Context, pantryID uuid.UUID) (*PantryShoppingListStats, error)
}
```

### Service Layer

**ShoppingListService Interface**
```go
type ShoppingListService interface {
    // Shopping List operations
    CreateShoppingList(ctx context.Context, req CreateShoppingListRequest) (*ShoppingList, error)
    GetShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) (*ShoppingList, error)
    GetShoppingListsByPantry(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID, filters ShoppingListFilters) ([]*ShoppingList, error)
    UpdateShoppingList(ctx context.Context, req UpdateShoppingListRequest) (*ShoppingList, error)
    DeleteShoppingList(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
    ChangeShoppingListStatus(ctx context.Context, req ChangeShoppingListStatusRequest) (*ShoppingList, error)

    // Shopping List Item operations
    AddItemToShoppingList(ctx context.Context, req AddShoppingListItemRequest) (*ShoppingListItemEntity, error)
    UpdateShoppingListItem(ctx context.Context, req UpdateShoppingListItemRequest) (*ShoppingListItemEntity, error)
    RemoveItemFromShoppingList(ctx context.Context, req RemoveShoppingListItemRequest) error
    MarkItemAsPurchased(ctx context.Context, req MarkItemPurchasedRequest) (*ShoppingListItemEntity, error)
    MarkItemAsNotPurchased(ctx context.Context, req MarkItemNotPurchasedRequest) (*ShoppingListItemEntity, error)

    // Bulk operations
    MarkMultipleItemsAsPurchased(ctx context.Context, req BulkMarkItemsPurchasedRequest) error
    MarkMultipleItemsAsNotPurchased(ctx context.Context, req BulkMarkItemsNotPurchasedRequest) error

    // Statistics
    GetShoppingListStatistics(ctx context.Context, shoppingListID uuid.UUID, userID uuid.UUID) (*ShoppingListStats, error)
    GetPantryShoppingListStatistics(ctx context.Context, pantryID uuid.UUID, userID uuid.UUID) (*PantryShoppingListStats, error)
}
```

## Database Schema

### Tables

**shopping_lists**
```sql
CREATE TABLE shopping_lists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);
```

**shopping_list_items**
```sql
CREATE TABLE shopping_list_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shopping_list_id UUID NOT NULL REFERENCES shopping_lists(id) ON DELETE CASCADE,
    product_variant_id UUID REFERENCES product_variants(variant_id) ON DELETE SET NULL,
    free_text_name VARCHAR(255),
    quantity_desired DECIMAL(10,3) NOT NULL CHECK (quantity_desired > 0),
    unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
    notes TEXT,
    is_purchased BOOLEAN NOT NULL DEFAULT FALSE,
    purchased_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Ensure either product_variant_id or free_text_name is provided, but not both
    CONSTRAINT check_item_reference CHECK (
        (product_variant_id IS NOT NULL AND free_text_name IS NULL) OR
        (product_variant_id IS NULL AND free_text_name IS NOT NULL)
    ),
    
    -- Ensure purchased_at is set when is_purchased is true
    CONSTRAINT check_purchased_at CHECK (
        (is_purchased = FALSE AND purchased_at IS NULL) OR
        (is_purchased = TRUE AND purchased_at IS NOT NULL)
    )
);
```

### Indexes

- Performance indexes on foreign keys and frequently queried columns
- Composite indexes for common query patterns
- Full-text search support for shopping list names

### Triggers

- Automatic `updated_at` timestamp updates
- Automatic `purchased_at` timestamp management
- Data consistency enforcement

## Features

### Core Functionality

1. **Shopping List Management**
   - Create shopping lists with name and optional description
   - Update shopping list details
   - Change status (active → completed → archived)
   - Delete shopping lists (cascades to items)

2. **Item Management**
   - Add items by product variant or free text
   - Update item details (quantity, notes, etc.)
   - Remove items from lists
   - Support for units of measure

3. **Purchase Tracking**
   - Mark individual items as purchased/not purchased
   - Bulk operations for multiple items
   - Automatic timestamp tracking
   - Purchase status persistence

4. **Statistics & Analytics**
   - Shopping list completion percentages
   - Item counts (total, purchased, remaining)
   - Pantry-level aggregated statistics
   - Progress tracking over time

### Advanced Features

1. **Flexible Item Types**
   - Product catalog integration
   - Free-text items for non-catalog products
   - Quantity and unit tracking
   - Optional notes per item

2. **Access Control**
   - Pantry-based permissions
   - User role validation
   - Secure data access patterns

3. **Data Integrity**
   - Database constraints
   - Business rule validation
   - Consistent state management

## Usage Examples

### Creating a Shopping List

```go
req := domain.CreateShoppingListRequest{
    PantryID:    pantryID,
    Name:        "Weekly Groceries",
    Description: &description,
    UserID:      userID,
}

shoppingList, err := service.CreateShoppingList(ctx, req)
```

### Adding Items

```go
// Add product variant item
req := domain.AddShoppingListItemRequest{
    ShoppingListID:   listID,
    ProductVariantID: &variantID,
    QuantityDesired:  2.5,
    UnitOfMeasureID:  &unitID,
    UserID:          userID,
}

// Add free text item
req := domain.AddShoppingListItemRequest{
    ShoppingListID:  listID,
    FreeTextName:    &itemName,
    QuantityDesired: 1.0,
    UserID:         userID,
}
```

### Bulk Purchase Operations

```go
req := domain.BulkMarkItemsPurchasedRequest{
    ItemIDs: []uuid.UUID{item1ID, item2ID, item3ID},
    UserID:  userID,
}

err := service.MarkMultipleItemsAsPurchased(ctx, req)
```

## Integration Points

### Dependencies

- **Pantry Service**: For access control and pantry validation
- **Product Catalog**: For product variant references
- **User Service**: For user validation and authentication
- **Authorization Service**: For permission checking

### Future Enhancements

1. **Recipe Integration**: Generate shopping lists from recipes
2. **Smart Suggestions**: AI-powered item recommendations
3. **Inventory Integration**: Auto-check against current inventory
4. **Sharing**: Share shopping lists between pantry members
5. **Templates**: Reusable shopping list templates
6. **Mobile Optimization**: Offline support and sync

## Error Handling

The shopping list feature implements comprehensive error handling:

- **Validation Errors**: Invalid input data, business rule violations
- **Authorization Errors**: Access denied, insufficient permissions
- **Not Found Errors**: Missing shopping lists, items, or related entities
- **Conflict Errors**: Concurrent modifications, constraint violations
- **System Errors**: Database failures, external service issues

All errors follow the application's standardized error response format with appropriate HTTP status codes and detailed error messages.
