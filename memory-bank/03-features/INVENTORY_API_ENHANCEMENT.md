# Inventory API Enhancement: Name/Label Fields for Frontend Display

## Overview

Enhanced inventory API responses to include human-readable name and label fields alongside entity IDs, enabling the frontend to display meaningful information to users without requiring additional API calls.

## Problem Statement

Previously, inventory API responses only included UUIDs for related entities (ProductVariantID, UnitOfMeasureID, LocationID, PantryID), requiring the frontend to make multiple additional API calls to fetch the corresponding names for display purposes.

## Solution Implemented

### 1. Enhanced Response Structure

Updated `InventoryItemResponse` to include name fields:

```go
type InventoryItemResponse struct {
    // Existing ID fields
    ID               uuid.UUID `json:"id"`
    PantryID         uuid.UUID `json:"pantry_id"`
    LocationID       *uuid.UUID `json:"location_id,omitempty"`
    ProductVariantID uuid.UUID `json:"product_variant_id"`
    UnitOfMeasureID  uuid.UUID `json:"unit_of_measure_id"`
    
    // NEW: Human-readable name fields
    PantryName       string    `json:"pantry_name"`
    LocationName     *string   `json:"location_name,omitempty"`
    ProductName      string    `json:"product_name"`
    ProductBrand     *string   `json:"product_brand,omitempty"`
    VariantName      string    `json:"variant_name"`
    VariantImageURL  *string   `json:"variant_image_url,omitempty"`
    CategoryName     string    `json:"category_name"`
    UnitName         string    `json:"unit_name"`
    UnitSymbol       string    `json:"unit_symbol"`
    
    // Other existing fields...
    Quantity         float64   `json:"quantity"`
    Status           InventoryItemStatus `json:"status"`
    // ...
}
```

### 2. New Domain Model

Created `InventoryItemWithRelations` to hold inventory items with all related data:

```go
type InventoryItemWithRelations struct {
    *InventoryItem
    PantryName      string
    LocationName    *string
    ProductName     string
    ProductBrand    *string
    VariantName     string
    VariantImageURL *string
    CategoryName    string
    UnitName        string
    UnitSymbol      string
}
```

### 3. Enhanced Repository Methods

Added new repository methods that fetch inventory items with all related data in a single query:

* `GetByIDWithRelations(id uuid.UUID) (*InventoryItemWithRelations, error)`
* `GetByPantryIDWithRelations(pantryID uuid.UUID, page, limit int) ([]*InventoryItemWithRelations, int64, error)`
* `GetExpiringItemsWithRelations(pantryID uuid.UUID, days int) ([]*InventoryItemWithRelations, error)`
* `GetLowStockItemsWithRelations(pantryID uuid.UUID) ([]*InventoryItemWithRelations, error)`
* `SearchItemsWithRelations(pantryID uuid.UUID, query string, page, limit int) ([]*InventoryItemWithRelations, int64, error)`

### 4. Optimized Database Queries

Implemented efficient JOIN queries to fetch all related data in a single database call:

```sql
SELECT i.*, 
    p.name as pantry_name,
    pl.name as location_name,
    pr.name as product_name,
    pr.brand as product_brand,
    pv.name as variant_name,
    pv.image_url as variant_image_url,
    c.name as category_name,
    u.name as unit_name,
    u.symbol as unit_symbol
FROM inventory_items i
JOIN pantries p ON i.pantry_id = p.pantry_id
LEFT JOIN pantry_locations pl ON i.location_id = pl.location_id
JOIN product_variants pv ON i.product_variant_id = pv.variant_id
JOIN products pr ON pv.product_id = pr.product_id
JOIN categories c ON pr.category_id = c.category_id
JOIN units_of_measure u ON i.unit_of_measure_id = u.unit_id
```

### 5. Updated Use Case Layer

Modified `GetPantryInventory` to return `[]*domain.InventoryItemResponse` instead of `[]*domain.InventoryItem` , with all name fields populated.

### 6. Enhanced Swagger Documentation

Updated all inventory endpoint swagger annotations to reflect the enhanced response structure:

* **Updated swagger models** in `internal/infra/web/handler/swagger_models.go`
* **Enhanced endpoint descriptions** to mention human-readable names
* **Updated response types** from generic `APIResponse` to specific `APIResponse{data=InventoryItemResponse}`
* **Regenerated documentation** using `swag init` command

**Updated Endpoints:**
* `GET /pantries/{pantryId}/inventory` - Returns `PaginatedResponse{data=[]InventoryItemResponse}`
* `POST /pantries/{pantryId}/inventory` - Returns `APIResponse{data=InventoryItemResponse}`
* `GET /inventory/{itemId}` - Returns `APIResponse{data=InventoryItemResponse}`
* `PUT /inventory/{itemId}` - Returns `APIResponse{data=InventoryItemResponse}`
* `POST /inventory/{itemId}/consume` - Returns `APIResponse{data=InventoryItemResponse}`

## Files Modified

### Domain Layer

* `internal/core/domain/inventory_requests.go` - Enhanced response structure
* `internal/core/domain/inventory.go` - Added `InventoryItemWithRelations` and new repository methods

### Repository Layer

* `internal/infra/persistence/postgres/inventory_item_repository.go` - Implemented new methods with JOIN queries

### Use Case Layer

* `internal/core/usecases/inventory_usecase.go` - Updated methods and added conversion logic

### Handler Layer

* `internal/infra/web/handler/inventory_handler.go` - Updated to use new response format
* `internal/infra/web/handler/swagger_models.go` - Enhanced InventoryItemResponse model

### Documentation Layer

* `docs/swagger.json` - Regenerated with enhanced response models
* `docs/swagger.yaml` - Regenerated with enhanced response models
* `docs/docs.go` - Regenerated with enhanced response models

## Benefits

### 1. Performance Improvement

* **Before**: Frontend needed N+1 API calls (1 for inventory + N for each related entity)
* **After**: Single API call returns all necessary data

### 2. Frontend Simplification

* No need for complex state management to track related entity names
* Immediate display of human-readable information
* Reduced loading states and error handling

### 3. Network Efficiency

* Reduced bandwidth usage
* Fewer HTTP requests
* Lower latency for inventory displays

### 4. Better User Experience

* Faster page loads
* Immediate display of meaningful information
* No placeholder text while loading related data

## Example API Response

### Before Enhancement

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "pantry_id": "456e7890-e89b-12d3-a456-426614174001",
  "product_variant_id": "789e0123-e89b-12d3-a456-426614174002",
  "unit_of_measure_id": "012e3456-e89b-12d3-a456-426614174003",
  "quantity": 5.0,
  "status": "fresh"
}
```

### After Enhancement

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "pantry_id": "456e7890-e89b-12d3-a456-426614174001",
  "pantry_name": "Main Kitchen",
  "product_variant_id": "789e0123-e89b-12d3-a456-426614174002",
  "product_name": "Organic Milk",
  "product_brand": "Farm Fresh",
  "variant_name": "1L Whole Milk",
  "category_name": "Dairy",
  "unit_of_measure_id": "012e3456-e89b-12d3-a456-426614174003",
  "unit_name": "Liter",
  "unit_symbol": "L",
  "quantity": 5.0,
  "status": "fresh"
}
```

## Backward Compatibility

* All existing ID fields are preserved
* Optional nested objects (`location`,     `product_variant`,     `unit_of_measure`) remain for backward compatibility
* No breaking changes to existing API contracts

## Testing

* ✅ Build verification successful
* ✅ All repository methods implemented
* ✅ Use case layer updated
* ✅ Handler layer updated
* ✅ No compilation errors
* ✅ **Swagger documentation regenerated with enhanced response models** ✅
* ✅ **Database JOIN query issues fixed** ✅

## Future Enhancements

1. **Caching**: Implement Redis caching for frequently accessed name mappings
2. **Selective Fields**: Add query parameters to control which name fields to include
3. **Localization**: Support for multi-language name fields
4. **Image Optimization**: Add thumbnail URLs for product variant images

## Impact

This enhancement significantly improves the frontend development experience and application performance by providing all necessary display information in a single API call, following the user preference for including name/label fields alongside entity IDs.
