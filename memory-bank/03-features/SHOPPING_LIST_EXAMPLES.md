# Shopping List Generation Examples

This document provides examples of how to use the Shopping List Generation feature in the Pantry Pal API.

## Overview

The Shopping List Generation feature automatically creates shopping lists based on:

- **Low Stock Items**: Items running below a configurable threshold
- **Empty Items**: Items that are completely out of stock
- **Expiring Items**: Items that will expire soon and need replacement
- **Recipe Requirements**: Items needed for specific recipes
- **Consumption Patterns**: Smart quantity recommendations based on usage history

## API Endpoint

```
POST /api/v1/pantries/{pantryId}/inventory/shopping-list
```

## Request Examples

### Example 1: Basic Low Stock Shopping List

```json
{
  "include_low_stock": true,
  "include_empty": true,
  "low_stock_threshold": 3.0
}
```

### Example 2: Comprehensive Shopping List with Consumption Data

```json
{
  "include_low_stock": true,
  "include_empty": true,
  "include_expiring_soon": true,
  "expiring_in_days": 7,
  "low_stock_threshold": 5.0,
  "use_consumption_data": true,
  "consumption_days": 30
}
```

### Example 3: Category-Specific Shopping List

```json
{
  "include_low_stock": true,
  "include_empty": true,
  "category_ids": [
    "550e8400-e29b-41d4-a716-446655440001",
    "550e8400-e29b-41d4-a716-446655440002"
  ],
  "low_stock_threshold": 2.0
}
```

### Example 4: Recipe-Based Shopping List

```json
{
  "include_recipe_items": true,
  "recipe_ids": [
    "550e8400-e29b-41d4-a716-446655440100",
    "550e8400-e29b-41d4-a716-446655440101"
  ],
  "exclude_available": true
}
```

## Response Examples

### Successful Shopping List Response

```json
{
  "success": true,
  "message": "Shopping list generated successfully",
  "data": {
    "total_items": 8,
    "low_stock_items": 3,
    "empty_items": 2,
    "expiring_items": 2,
    "recipe_items": 1,
    "generated_at": "2024-01-15T10:30:00Z",
    "items": [
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440001",
        "product_name": "Organic Milk",
        "variant_name": "1 Liter",
        "category_id": "550e8400-e29b-41d4-a716-446655440010",
        "category_name": "Dairy",
        "suggested_quantity": 4.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440020",
        "unit_name": "pieces",
        "unit_symbol": "pcs",
        "current_quantity": 1.0,
        "reasons": [
          {
            "type": "low_stock",
            "description": "Item is running low",
            "details": {
              "current_quantity": 1.0,
              "threshold": 3.0
            }
          }
        ],
        "priority": "medium",
        "estimated_price": 3.99,
        "last_purchase_price": 3.89,
        "last_purchase_date": "2024-01-10",
        "consumption_rate": 0.5,
        "days_until_empty": 2,
        "recommended_brands": ["Organic Valley", "Horizon"]
      },
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440002",
        "product_name": "Bread",
        "variant_name": "Whole Wheat Loaf",
        "category_id": "550e8400-e29b-41d4-a716-446655440011",
        "category_name": "Bakery",
        "suggested_quantity": 2.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440020",
        "unit_name": "pieces",
        "unit_symbol": "pcs",
        "current_quantity": 0.0,
        "reasons": [
          {
            "type": "empty",
            "description": "Item is out of stock",
            "details": {
              "current_quantity": 0.0
            }
          }
        ],
        "priority": "high",
        "estimated_price": 4.50,
        "consumption_rate": 0.3,
        "recommended_brands": ["Dave's Killer Bread", "Ezekiel"]
      },
      {
        "product_variant_id": "550e8400-e29b-41d4-a716-446655440003",
        "product_name": "Yogurt",
        "variant_name": "Greek Plain 500g",
        "category_id": "550e8400-e29b-41d4-a716-446655440010",
        "category_name": "Dairy",
        "suggested_quantity": 3.0,
        "unit_of_measure_id": "550e8400-e29b-41d4-a716-446655440020",
        "unit_name": "pieces",
        "unit_symbol": "pcs",
        "current_quantity": 2.0,
        "reasons": [
          {
            "type": "expiring_soon",
            "description": "Items expiring soon need replacement",
            "details": {
              "expiring_quantity": 2.0,
              "expiring_in_days": 7,
              "expiring_items": 2
            }
          }
        ],
        "priority": "medium",
        "estimated_price": 5.99
      }
    ],
    "summary": {
      "total_items": 8,
      "high_priority_items": 2,
      "medium_priority_items": 4,
      "low_priority_items": 2,
      "estimated_total": 47.85,
      "categories_count": 4,
      "top_categories": [
        {
          "category_id": "550e8400-e29b-41d4-a716-446655440010",
          "category_name": "Dairy",
          "item_count": 3,
          "estimated_cost": 18.47
        },
        {
          "category_id": "550e8400-e29b-41d4-a716-446655440011",
          "category_name": "Bakery",
          "item_count": 2,
          "estimated_cost": 12.50
        }
      ]
    }
  }
}
```

## Key Features

### 1. Smart Quantity Recommendations
- **Threshold-Based**: Suggests quantities to reach double the threshold
- **Consumption-Based**: Uses historical consumption data to predict needs
- **Time-Based**: Calculates quantities for specific time periods

### 2. Priority System
- **High Priority**: Empty items that are essential
- **Medium Priority**: Low stock items and expiring items
- **Low Priority**: Items with adequate stock but approaching threshold

### 3. Multiple Criteria Support
- **Low Stock Detection**: Configurable thresholds per request
- **Expiration Tracking**: Items expiring within specified days
- **Recipe Integration**: Items needed for planned recipes
- **Category Filtering**: Focus on specific product categories

### 4. Consumption Analytics
- **Usage Patterns**: Analyzes historical consumption rates
- **Predictive Quantities**: Suggests amounts based on usage trends
- **Days Until Empty**: Estimates when items will run out

### 5. Price Intelligence
- **Estimated Prices**: Based on recent purchase history
- **Last Purchase Tracking**: Shows previous purchase prices and dates
- **Total Cost Estimation**: Calculates expected shopping trip cost

## Use Cases

### 1. Weekly Grocery Planning
```json
{
  "include_low_stock": true,
  "include_empty": true,
  "include_expiring_soon": true,
  "expiring_in_days": 7,
  "use_consumption_data": true,
  "consumption_days": 7
}
```

### 2. Emergency Shopping (High Priority Only)
```json
{
  "include_empty": true,
  "low_stock_threshold": 1.0
}
```

### 3. Meal Prep Shopping
```json
{
  "include_recipe_items": true,
  "recipe_ids": ["recipe1", "recipe2", "recipe3"],
  "include_low_stock": true
}
```

### 4. Category-Specific Restocking
```json
{
  "include_low_stock": true,
  "include_empty": true,
  "category_ids": ["dairy_category_id"],
  "low_stock_threshold": 2.0
}
```

## Integration with Other Features

### Recipe Consumption Integration
- Shopping lists can include items needed for specific recipes
- Automatically excludes items you already have sufficient quantities of
- Suggests quantities based on recipe serving sizes

### Consumption Pattern Analysis
- Uses data from recipe consumption and manual consumption tracking
- Provides intelligent quantity recommendations
- Predicts when items will run out based on usage patterns

### Expiration Management
- Identifies items expiring soon that need replacement
- Helps reduce food waste by planning replacements
- Prioritizes items based on expiration urgency

## Error Handling

The system gracefully handles various scenarios:
- Missing product information (uses placeholders)
- Invalid category filters
- No items meeting criteria (returns empty list)
- Permission errors for pantry access

## Future Enhancements

- **Store Integration**: Group items by preferred stores
- **Brand Preferences**: Suggest preferred brands based on purchase history
- **Seasonal Adjustments**: Adjust quantities based on seasonal consumption patterns
- **Budget Constraints**: Generate lists within specified budget limits
- **Nutritional Balance**: Suggest items to maintain nutritional balance
