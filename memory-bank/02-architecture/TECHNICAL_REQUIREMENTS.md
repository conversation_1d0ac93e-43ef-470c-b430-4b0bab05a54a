# Pantry Pal Technical Requirements for Development

**Version:** 1.0  
**Date:** October 28, 2023  
**Author:** [Your Name/Team Name]

## 1. Purpose

This document specifies the core technical requirements, technology stack, and key implementation details for the Pantry Pal backend system. It builds upon the Product Requirements Document (PRD v2.1) and establishes the technical foundation for development. Adherence to `BACKEND_RULES_AND_GUIDES_PantryPal_v1.1.md` is mandatory.

## 2. Technology Stack

The backend system **SHALL** utilize the following primary technologies:

2.1.  **Programming Language:** Go (Golang) - latest stable version (e.g., 1.20+).
2.2.  **Web Framework:** Fiber v2.
2.3.  **Database:** PostgreSQL - latest stable version (e.g., 14+).
2.4.  **ORM:** GORM (github.com/go-gorm/gorm).
2.5.  **Logging:** Zerolog (github.com/rs/zerolog).
2.6.  **Configuration:** Koanf (github.com/knadh/koanf/v2).
2.7.  **Caching/Auxiliary Data Store:** Redis - latest stable version (e.g., 6+). Client: `github.com/go-redis/redis/v8`.
2.8.  **HTTP Client (for external calls):** Resty v2 (github.com/go-resty/resty/v2).
2.9.  **Authentication Library (JWT):** `github.com/golang-jwt/jwt/v5`.
2.10. **Authorization Library (RBAC):** `github.com/casbin/casbin/v2` with `github.com/casbin/gorm-adapter/v3`.
2.11. **Database Migrations:** `github.com/golang-migrate/migrate`.
2.12. **Input Validation:** `github.com/go-playground/validator/v10`.
2.13. **UUID Generation:** `github.com/google/uuid`.

## 3. Architectural Requirements

3.1.  **Clean Architecture:** The system **MUST** implement Clean Architecture principles as detailed in the "Backend Rules and Guides" document. The proposed directory structure in said document **SHALL** be followed.
3.2.  **Modularity:** The system **MUST** be designed with modular components to promote separation of concerns and testability.

## 4. Core System Components & Functionality

### 4.1. Configuration Management
4.1.1. The system **MUST** load configuration from YAML files (e.g., `config.yaml`) and environment variables using Koanf.
4.1.2. Environment variables (prefixed, e.g., `APP_`) **MUST** override file configurations.
4.1.3. Sensitive data (e.g., `APP_AUTH_JWT_SECRET`, `APP_DATABASE_PASSWORD`) **MUST** be loaded exclusively from environment variables.
4.1.4. Configuration **MUST** be structured into component-specific Go structs (e.g., `ServerConfig`, `DatabaseConfig`, `AuthConfig`, `RedisConfig`, `AppSettings`) as detailed in `BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`.

### 4.2. Logging
4.2.1. The system **MUST** use Zerolog for structured JSON logging in production environments and human-readable console logging in development.
4.2.2. Logs **MUST** include a unique `request_id` for tracing API requests through the system. This ID **SHALL** be generated by middleware.
4.2.3. Contextual information (e.g., `user_id`, `pantry_id`, `component`) **MUST** be included in logs where relevant.
4.2.4. Appropriate log levels (Debug, Info, Warn, Error, Fatal) **MUST** be used.

### 4.3. Error Handling
4.3.1. The system **MUST** implement a global error handler in the Fiber web layer to catch unhandled errors and return standardized JSON error responses as per the defined `APIResponse` structure.
4.3.2. Custom application errors, inheriting from a base `AppError` struct (defined in `internal/infra/errors/`), **MUST** be used to provide meaningful error codes, messages, and HTTP statuses.

### 4.4. Authentication (JWT)
4.4.1. The system **MUST** implement JWT-based authentication.
4.4.2. Upon successful login, an `AuthService` (implemented in `internal/infra/auth/`) **MUST** issue:
    *   A short-lived Access Token (configurable, e.g., 15-30 minutes).
    *   A long-lived Refresh Token (configurable, e.g., 7-30 days).
4.4.3. Access Tokens **MUST** contain `user_id` and standard JWT claims (`exp`, `iat`, `sub`, `iss`).
4.4.4. Refresh Tokens **MUST** contain `user_id`, a unique JWT ID (`jti`), and standard claims.
4.4.5. Refresh Tokens **MUST** be delivered to the client via an HTTP-only, secure, SameSite cookie. Access Tokens **MAY** be returned in the response body.
4.4.6. Cryptographic hashes of Refresh Tokens (using bcrypt) **MUST** be stored in a dedicated PostgreSQL table (`refresh_tokens`) with `user_id`, `token_hash`, `expires_at`, and `is_revoked` fields.
4.4.7. A `POST /auth/refresh` endpoint **MUST** allow clients to obtain a new Access Token (and a new Refresh Token, rotating the old one by revoking it and storing the new one) using a valid, non-revoked Refresh Token.
4.4.8. A `POST /auth/logout` endpoint **MUST** revoke the user's active Refresh Token(s) in the database.
4.4.9. JWT validation middleware (`internal/infra/web/middleware/auth_jwt.go`) **MUST** protect authenticated routes, extracting claims and storing `userID` in `fiber.Ctx.Locals()`.

### 4.5. Authorization (Casbin)
4.5.1. The system **MUST** implement RBAC with domain support using Casbin for fine-grained, per-pantry authorization.
4.5.2. The Casbin model (`internal/infra/authorization/model.conf`) **MUST** define `request_definition` as `r = sub, dom, obj, act` (Subject, Domain, Object, Action).
4.5.3. Casbin policies (`p` rules) and role assignments (`g` rules) **MUST** be persisted in the PostgreSQL database using the `gorm-adapter/v3`.
4.5.4. `g` rules (e.g., `g, user_id, role_name, pantry_id`) **MUST** be dynamically managed (added/removed) by use cases when `PantryMembership` roles are created, updated, or deleted.
4.5.5. Casbin authorization middleware (`internal/infra/web/middleware/authz_casbin.go`) **MUST** run after JWT authentication to enforce policies on API requests, using `userID` from locals, `pantryID` (domain) from path/body, request path (object), and HTTP method (action).

### 4.6. Database Management (PostgreSQL & GORM)
4.6.1. Domain models defined as Go structs in `core/domain/` **MUST** be mapped to PostgreSQL tables using separate GORM model structs in the `infra/persistence/postgres/` layer. These GORM structs will use GORM tags. Conversion functions between domain and GORM models **MUST** be provided.
4.6.2. Database schema migrations **MUST** be managed by `golang-migrate/migrate` using SQL files. `AutoMigrate` **SHALL NOT** be used for production schema management, except potentially for the `casbin_rule` table if deemed safe.
4.6.3. Database connection pooling **MUST** be configured (e.g., `MaxIdleConns`, `MaxOpenConns`, `ConnMaxLifetime`) and managed by GORM.
4.6.4. Business operations spanning multiple write operations (e.g., creating a purchase and its items, then updating inventory) **MUST** use database transactions to ensure atomicity. This **SHOULD** be handled within repository methods or via a Unit of Work pattern.

### 4.7. Idempotency
4.7.1. Critical state-changing API operations (POST, PUT, PATCH) **MUST** support idempotency.
4.7.2. Clients **SHALL** provide an `Idempotency-Key` (UUID v4) in the HTTP header.
4.7.3. An `IdempotencyService` (implemented in `internal/infra/persistence/redis/`) using Redis **MUST** be implemented to:
    *   Store a "processing" placeholder for an incoming key with a short TTL.
    *   Cache the final HTTP status code and response body upon successful completion (2xx) with a configurable TTL.
    *   Return the cached response if a subsequent request with the same key is received within the TTL.
    *   Return a `409 Conflict` (or similar) if a request with the same key is currently being processed.
    *   Clear the "processing" placeholder or mark it as failed if the original request results in a non-2xx status, allowing client retries.
4.7.4. An idempotency middleware (`internal/infra/web/middleware/idempotency.go`) in Fiber **MUST** orchestrate this workflow, running early in the request chain.

### 4.8. Domain Events
4.8.1. The system **SHALL** utilize domain events to capture significant business occurrences (e.g., `UserRegisteredEvent`, `InventoryItemUsedEvent`, `PantryMemberInvitedEvent`).
4.8.2. Domain events **MUST** be defined as structs in `internal/core/domain/events.go`.
4.8.3. Aggregate roots (domain entities like `User`, `Pantry`, `InventoryItem`) **SHALL** provide methods to add, retrieve, and clear a transient list of domain events.
4.8.4. A synchronous `EventDispatcher` interface **MUST** be defined in `core/usecases/`. An implementation (e.g., `EventHandlers` struct in `core/usecases/`) **MUST** process these events after a use case successfully completes its primary operation (typically after database transaction commit or successful save).

### 4.9. API Design & Response Convention
4.9.1. APIs **MUST** follow RESTful principles.
4.9.2. A standard JSON response structure (`APIResponse` with `success`, `data`, `error`, `message` fields) **MUST** be used, as detailed and exemplified in `BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md`. Helper functions for constructing these responses **SHALL** be provided.
4.9.3. HTTP status codes **MUST** be used appropriately (200, 201, 204, 400, 401, 403, 404, 409, 500, etc.).
4.9.4. Pagination and filtering conventions as detailed in `BACKEND_CODING_EXAMPLES_PantryPal_v1.1.md` **MUST** be implemented for list endpoints.

### 4.10. Graceful Shutdown
4.10.1. The application entry point (`cmd/api/main.go`) **MUST** implement graceful shutdown logic to:
    *   Stop accepting new HTTP requests via Fiber's `Shutdown` method.
    *   Allow in-flight requests to complete within a timeout.
    *   Close the PostgreSQL database connection pool.
    *   Close the Redis client connection.
    *   Signal and wait for any background goroutines (e.g., cron jobs, event consumers) to terminate cleanly using `context.Context` and `sync.WaitGroup`.
4.10.2. This shutdown sequence **MUST** be triggered by OS signals (SIGINT, SIGTERM).

## 5. Non-Functional Requirements (NFRs) Reference

The system **MUST** meet all Non-Functional Requirements as specified in Section 4 of the `PRD_PantryPal_v2.1.md`, including:
5.1.  Performance (API Response Times, Throughput, Data Volume).
5.2.  Scalability (Horizontal Scaling, Read Replicas).
5.3.  Security (Authentication, Authorization, Encryption, Vulnerability Management, etc.).
5.4.  Reliability & Availability (Uptime, Error Handling, Data Integrity).
5.5.  Maintainability (Code Quality, Testability, Observability).
5.6.  Usability (API Clarity, Idempotency - also covered in 4.7).

## 6. Development & Deployment

6.1.  **Containerization:** The application **MUST** be containerized using Docker. A multi-stage Dockerfile **SHALL** be used for optimized production images, minimizing image size and build dependencies.
6.2.  **Local Development:** `docker-compose.yml` **SHALL** be provided for setting up local PostgreSQL and Redis instances, facilitating a consistent development environment.
6.3.  **Testing:** Unit, integration, and API/E2E tests **MUST** be implemented as per the "Backend Rules and Guides." Test coverage **SHOULD** be monitored.
6.4.  **CI/CD:** A Continuous Integration/Continuous Deployment (CI/CD) pipeline (e.g., GitHub Actions, GitLab CI) **SHALL** be established to automate linting, testing, building container images, and deploying to staging/production environments.
6.5.  **API Documentation:** OpenAPI/Swagger documentation **MUST** be generated (e.g., using `swag`) and kept up-to-date.

---

*For detailed coding examples and implementation patterns, refer to [`04-implementation/BACKEND_CODING_EXAMPLES.md`](../04-implementation/BACKEND_CODING_EXAMPLES.md)*
