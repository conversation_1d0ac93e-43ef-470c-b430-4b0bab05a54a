# Backend Coding Examples and Patterns

**Version:** 1.1  
**Date:** October 28, 2023  
**Author:** [Your Name/Team Name]

## 1. Purpose

This document provides concrete coding examples and implementation patterns for the Pantry Pal backend system. It complements the Development Rules and Guides by showing practical implementations of the required patterns and standards.

## 2. Error Handling Examples

### 2.1 Custom Error Types

```go
// internal/infra/errors/errors.go
package errors

import (
    "fmt"
    "net/http"
)

type AppError struct {
    Code       string `json:"code"`
    Message    string `json:"message"`
    HTTPStatus int    `json:"-"`
    Err        error  `json:"-"`
}

func (e *AppError) Error() string {
    if e.Err != nil {
        return fmt.Sprintf("%s: %v", e.Message, e.Err)
    }
    return e.Message
}

func (e *AppError) Unwrap() error {
    return e.Err
}

// Common error constructors
func NewValidationError(message string) *AppError {
    return &AppError{
        Code:       "VALIDATION_ERROR",
        Message:    message,
        HTTPStatus: http.StatusBadRequest,
    }
}

func NewNotFoundError(resource string) *AppError {
    return &AppError{
        Code:       "NOT_FOUND",
        Message:    fmt.Sprintf("%s not found", resource),
        HTTPStatus: http.StatusNotFound,
    }
}

func NewInternalError(err error) *AppError {
    return &AppError{
        Code:       "INTERNAL_ERROR",
        Message:    "An internal error occurred",
        HTTPStatus: http.StatusInternalServerError,
        Err:        err,
    }
}
```

### 2.2 Error Wrapping Pattern

```go
// Example in repository layer
func (r *pantryRepository) GetByID(ctx context.Context, id string) (*domain.Pantry, error) {
    var pantryModel PantryModel
    
    if err := r.db.WithContext(ctx).First(&pantryModel, "pantry_id = ?", id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, apperrors.NewNotFoundError("Pantry")
        }
        return nil, fmt.Errorf("failed to get pantry by ID %s: %w", id, err)
    }
    
    return pantryModel.ToDomain(), nil
}
```

## 3. API Response Patterns

### 3.1 Standard Response Structure

```go
// internal/infra/web/handler/response.go
package handler

import (
    "github.com/gofiber/fiber/v2"
    "pantry-pal/internal/infra/errors"
)

type APIResponse struct {
    Success bool        `json:"success"`
    Data    interface{} `json:"data,omitempty"`
    Error   *ErrorInfo  `json:"error,omitempty"`
    Message string      `json:"message,omitempty"`
    Meta    *MetaInfo   `json:"meta,omitempty"`
}

type ErrorInfo struct {
    Code    string      `json:"code"`
    Message string      `json:"message"`
    Details interface{} `json:"details,omitempty"`
}

type MetaInfo struct {
    Pagination *PaginationMetadata `json:"pagination,omitempty"`
}

type PaginationMetadata struct {
    Page       int `json:"page"`
    Limit      int `json:"limit"`
    Total      int `json:"total"`
    TotalPages int `json:"total_pages"`
}

// Response helper functions
func SuccessResponse(c *fiber.Ctx, data interface{}, message ...string) error {
    response := APIResponse{
        Success: true,
        Data:    data,
    }
    
    if len(message) > 0 {
        response.Message = message[0]
    }
    
    return c.JSON(response)
}

func ErrorResponse(c *fiber.Ctx, err error) error {
    var appErr *errors.AppError
    var statusCode int
    var errorInfo ErrorInfo
    
    if errors.As(err, &appErr) {
        statusCode = appErr.HTTPStatus
        errorInfo = ErrorInfo{
            Code:    appErr.Code,
            Message: appErr.Message,
        }
    } else {
        statusCode = 500
        errorInfo = ErrorInfo{
            Code:    "INTERNAL_ERROR",
            Message: "An internal error occurred",
        }
    }
    
    response := APIResponse{
        Success: false,
        Error:   &errorInfo,
    }
    
    return c.Status(statusCode).JSON(response)
}

func PaginatedResponse(c *fiber.Ctx, data interface{}, pagination PaginationMetadata) error {
    response := APIResponse{
        Success: true,
        Data:    data,
        Meta: &MetaInfo{
            Pagination: &pagination,
        },
    }
    
    return c.JSON(response)
}
```

### 3.2 Pagination Implementation

```go
// Example handler with pagination
func (h *pantryHandler) GetInventoryItems(c *fiber.Ctx) error {
    pantryID := c.Params("pantryId")
    
    // Parse pagination parameters
    page := c.QueryInt("page", 1)
    limit := c.QueryInt("limit", 20)
    sortBy := c.Query("sort_by", "created_at")
    sortOrder := c.Query("sort_order", "desc")
    
    // Parse filters
    filters := map[string]interface{}{}
    if category := c.Query("category"); category != "" {
        filters["category"] = category
    }
    if location := c.Query("location"); location != "" {
        filters["location"] = location
    }
    
    // Call use case
    result, err := h.inventoryUsecase.GetInventoryItems(c.Context(), pantryID, page, limit, sortBy, sortOrder, filters)
    if err != nil {
        return ErrorResponse(c, err)
    }
    
    // Build pagination metadata
    pagination := PaginationMetadata{
        Page:       page,
        Limit:      limit,
        Total:      result.Total,
        TotalPages: (result.Total + limit - 1) / limit,
    }
    
    return PaginatedResponse(c, result.Items, pagination)
}
```

## 4. Domain Events Pattern

### 4.1 Event Definitions

```go
// internal/core/domain/events.go
package domain

import "time"

type DomainEvent interface {
    EventType() string
    OccurredAt() time.Time
    AggregateID() string
}

type BaseEvent struct {
    Type        string    `json:"type"`
    Timestamp   time.Time `json:"timestamp"`
    AggregateId string    `json:"aggregate_id"`
}

func (e BaseEvent) EventType() string {
    return e.Type
}

func (e BaseEvent) OccurredAt() time.Time {
    return e.Timestamp
}

func (e BaseEvent) AggregateID() string {
    return e.AggregateId
}

// Specific events
type InventoryItemAddedEvent struct {
    BaseEvent
    PantryID      string `json:"pantry_id"`
    ProductName   string `json:"product_name"`
    Quantity      int    `json:"quantity"`
    ExpirationDate *time.Time `json:"expiration_date,omitempty"`
}

type InventoryItemUsedEvent struct {
    BaseEvent
    PantryID     string `json:"pantry_id"`
    ProductName  string `json:"product_name"`
    QuantityUsed int    `json:"quantity_used"`
    RemainingQty int    `json:"remaining_quantity"`
}
```

### 4.2 Aggregate Root with Events

```go
// Example aggregate root
type InventoryItem struct {
    ID             string
    PantryID       string
    ProductVariant *ProductVariant
    Quantity       int
    ExpirationDate *time.Time
    // ... other fields
    
    events []DomainEvent
}

func (i *InventoryItem) AddEvent(event DomainEvent) {
    i.events = append(i.events, event)
}

func (i *InventoryItem) GetEvents() []DomainEvent {
    return i.events
}

func (i *InventoryItem) ClearEvents() {
    i.events = nil
}

func (i *InventoryItem) Use(quantity int) error {
    if i.Quantity < quantity {
        return errors.NewValidationError("Insufficient quantity")
    }
    
    i.Quantity -= quantity
    
    // Raise domain event
    event := InventoryItemUsedEvent{
        BaseEvent: BaseEvent{
            Type:        "inventory_item_used",
            Timestamp:   time.Now(),
            AggregateId: i.ID,
        },
        PantryID:     i.PantryID,
        ProductName:  i.ProductVariant.Name,
        QuantityUsed: quantity,
        RemainingQty: i.Quantity,
    }
    
    i.AddEvent(event)
    return nil
}
```

## 5. Use Case Pattern

### 5.1 Use Case Implementation

```go
// internal/core/usecases/inventory_usecase.go
package usecases

import (
    "context"
    "pantry-pal/internal/core/domain"
)

type InventoryUsecase struct {
    inventoryRepo domain.InventoryRepository
    eventHandler  EventHandler
    logger        Logger
}

func NewInventoryUsecase(
    inventoryRepo domain.InventoryRepository,
    eventHandler EventHandler,
    logger Logger,
) *InventoryUsecase {
    return &InventoryUsecase{
        inventoryRepo: inventoryRepo,
        eventHandler:  eventHandler,
        logger:        logger,
    }
}

func (u *InventoryUsecase) UseInventoryItem(ctx context.Context, itemID string, quantity int) error {
    // Get the inventory item
    item, err := u.inventoryRepo.GetByID(ctx, itemID)
    if err != nil {
        return fmt.Errorf("failed to get inventory item: %w", err)
    }
    
    // Use the item (business logic)
    if err := item.Use(quantity); err != nil {
        return fmt.Errorf("failed to use inventory item: %w", err)
    }
    
    // Save the updated item
    if err := u.inventoryRepo.Update(ctx, item); err != nil {
        return fmt.Errorf("failed to update inventory item: %w", err)
    }
    
    // Dispatch events
    for _, event := range item.GetEvents() {
        if err := u.eventHandler.Handle(ctx, event); err != nil {
            u.logger.Error().Err(err).Msg("Failed to handle domain event")
            // Don't fail the operation for event handling errors
        }
    }
    
    item.ClearEvents()
    return nil
}
```

## 6. Repository Pattern

### 6.1 Repository Implementation

```go
// internal/infra/persistence/postgres/inventory_repository.go
package postgres

import (
    "context"
    "fmt"
    "pantry-pal/internal/core/domain"
    "gorm.io/gorm"
)

type inventoryRepository struct {
    db *gorm.DB
}

func NewInventoryRepository(db *gorm.DB) domain.InventoryRepository {
    return &inventoryRepository{db: db}
}

func (r *inventoryRepository) Create(ctx context.Context, item *domain.InventoryItem) error {
    model := FromDomainInventoryItem(item)
    
    if err := r.db.WithContext(ctx).Create(&model).Error; err != nil {
        return fmt.Errorf("failed to create inventory item: %w", err)
    }
    
    return nil
}

func (r *inventoryRepository) GetByID(ctx context.Context, id string) (*domain.InventoryItem, error) {
    var model InventoryItemModel
    
    if err := r.db.WithContext(ctx).
        Preload("ProductVariant").
        Preload("ProductVariant.Product").
        First(&model, "inventory_item_id = ?", id).Error; err != nil {
        
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return nil, apperrors.NewNotFoundError("Inventory item")
        }
        return nil, fmt.Errorf("failed to get inventory item: %w", err)
    }
    
    return model.ToDomain(), nil
}

func (r *inventoryRepository) Update(ctx context.Context, item *domain.InventoryItem) error {
    model := FromDomainInventoryItem(item)
    
    if err := r.db.WithContext(ctx).Save(&model).Error; err != nil {
        return fmt.Errorf("failed to update inventory item: %w", err)
    }
    
    return nil
}
```

## 7. Middleware Examples

### 7.1 JWT Authentication Middleware

```go
// internal/infra/web/middleware/auth_jwt.go
package middleware

import (
    "strings"
    "github.com/gofiber/fiber/v2"
    "github.com/golang-jwt/jwt/v5"
)

func JWTAuth(jwtSecret string) fiber.Handler {
    return func(c *fiber.Ctx) error {
        authHeader := c.Get("Authorization")
        if authHeader == "" {
            return c.Status(401).JSON(fiber.Map{
                "success": false,
                "error": fiber.Map{
                    "code":    "UNAUTHORIZED",
                    "message": "Authorization header required",
                },
            })
        }
        
        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        if tokenString == authHeader {
            return c.Status(401).JSON(fiber.Map{
                "success": false,
                "error": fiber.Map{
                    "code":    "UNAUTHORIZED",
                    "message": "Bearer token required",
                },
            })
        }
        
        token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
            return []byte(jwtSecret), nil
        })
        
        if err != nil || !token.Valid {
            return c.Status(401).JSON(fiber.Map{
                "success": false,
                "error": fiber.Map{
                    "code":    "UNAUTHORIZED",
                    "message": "Invalid token",
                },
            })
        }
        
        claims, ok := token.Claims.(jwt.MapClaims)
        if !ok {
            return c.Status(401).JSON(fiber.Map{
                "success": false,
                "error": fiber.Map{
                    "code":    "UNAUTHORIZED",
                    "message": "Invalid token claims",
                },
            })
        }
        
        userID, ok := claims["user_id"].(string)
        if !ok {
            return c.Status(401).JSON(fiber.Map{
                "success": false,
                "error": fiber.Map{
                    "code":    "UNAUTHORIZED",
                    "message": "Invalid user ID in token",
                },
            })
        }
        
        c.Locals("userID", userID)
        return c.Next()
    }
}
```

## 8. Configuration Management

### 8.1 Configuration Structure

```go
// internal/infra/config/config.go
package config

import (
    "github.com/knadh/koanf/v2"
    "github.com/knadh/koanf/parsers/yaml"
    "github.com/knadh/koanf/providers/env"
    "github.com/knadh/koanf/providers/file"
)

type Config struct {
    Server   ServerConfig   `koanf:"server"`
    Database DatabaseConfig `koanf:"database"`
    Redis    RedisConfig    `koanf:"redis"`
    Auth     AuthConfig     `koanf:"auth"`
    App      AppConfig      `koanf:"app"`
}

type ServerConfig struct {
    Host string `koanf:"host"`
    Port int    `koanf:"port"`
}

type DatabaseConfig struct {
    Host     string `koanf:"host"`
    Port     int    `koanf:"port"`
    Name     string `koanf:"name"`
    User     string `koanf:"user"`
    Password string `koanf:"password"`
    SSLMode  string `koanf:"ssl_mode"`
}

type AuthConfig struct {
    JWTSecret           string `koanf:"jwt_secret"`
    AccessTokenExpiry   string `koanf:"access_token_expiry"`
    RefreshTokenExpiry  string `koanf:"refresh_token_expiry"`
}

func Load() (*Config, error) {
    k := koanf.New(".")
    
    // Load from config file
    if err := k.Load(file.Provider("config.yaml"), yaml.Parser()); err != nil {
        return nil, fmt.Errorf("failed to load config file: %w", err)
    }
    
    // Load from environment variables (with APP_ prefix)
    if err := k.Load(env.Provider("APP_", ".", func(s string) string {
        return strings.Replace(strings.ToLower(
            strings.TrimPrefix(s, "APP_")), "_", ".", -1)
    }), nil); err != nil {
        return nil, fmt.Errorf("failed to load environment variables: %w", err)
    }
    
    var config Config
    if err := k.Unmarshal("", &config); err != nil {
        return nil, fmt.Errorf("failed to unmarshal config: %w", err)
    }
    
    return &config, nil
}
```

---

*These examples demonstrate the practical implementation of the architectural patterns and coding standards required for the Pantry Pal backend system.*
