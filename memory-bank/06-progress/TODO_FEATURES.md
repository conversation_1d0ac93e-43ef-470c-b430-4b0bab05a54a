# Pantry Pal - TODO Features & Implementation Tasks

## 📊 **Implementation Progress Overview**

* **✅ Completed Features:** ~80% (Core business logic, inventory, recipes, shopping lists, notifications, password change)
* **🚧 In Progress:** ~5% (Architecture ready, partial implementation)
* **❌ Not Started:** ~15% (Supporting systems, enhancements)

---

## 🔴 **HIGH PRIORITY - Core MVP Features**

### 1. Enhanced Shopping Lists ❌

**Status:** Core Completed, Enhancements Needed
**Priority:** High
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Enhanced Shopping List Features**
  + [ ] Receipt image storage for completed shopping lists
  + [ ] Store information tracking (name, location, contact)
  + [ ] Price per unit tracking for purchased items
  + [ ] Purchase history through completed shopping lists
  + [ ] Cost analysis and spending insights

* [ ] **Domain Enhancements**
  + [ ] Add receipt storage to shopping list entity
  + [ ] Store information fields
  + [ ] Enhanced price tracking for purchased items
  + [ ] Purchase completion workflow

* [ ] **API Enhancements**
  + [ ] Receipt upload endpoints for shopping lists
  + [ ] Store information management
  + [ ] Enhanced shopping list statistics with cost data
  + [ ] Purchase history via completed shopping lists

#### **Requirements Reference:**

```
- Store receipt images with completed shopping lists
- Track store information for shopping trips
- Maintain purchase history through shopping list completion
- Provide cost analysis and spending insights
```

### 2. Pantry-Specific Settings ❌

**Status:** Not Started  
**Priority:** High  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `PantrySettings` entity
  + [ ] Settings validation logic
  + [ ] Default settings initialization

* [ ] **Repository Layer**
  + [ ] `PantrySettingsRepository` implementation
  + [ ] Database migration for pantry_settings table

* [ ] **Use Cases**
  + [ ] `ConfigurePantrySettings` use case
  + [ ] `GetPantrySettings` use case
  + [ ] `UpdateLowStockThresholds` use case
  + [ ] `SetDefaultLocation` use case

* [ ] **HTTP API**
  + [ ] Settings CRUD endpoints
  + [ ] Threshold configuration endpoints

#### **Settings to Implement:**

* [ ] Low stock thresholds per product/category
* [ ] Default storage locations
* [ ] Preferred currency settings
* [ ] Preferred units of measure
* [ ] Notification preferences

### 3. Password Change Functionality ✅

**Status:** Completed
**Priority:** High
**Estimated Effort:** 1 day

#### **Completed Components:**

* [x] **Use Cases**
  + [x] `ChangePassword` use case with current password verification
  + [x] Password strength validation
  + [x] Security event logging

* [x] **HTTP API**
  + [x] Complete password change handler implementation
  + [x] Current password verification
  + [x] New password validation

#### **Current Status:**

```go
// internal/infra/web/handler/user_handler.go:175
// Fully implemented with security validation and token revocation
func (h *UserHandler) ChangePassword(c *fiber.Ctx) error
```

### 4. Usage Tracking & Analytics ❌

**Status:** Partial (consumption exists, no detailed logs)  
**Priority:** High  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `UsageLog` entity for detailed consumption tracking
  + [ ] Usage analytics aggregation
  + [ ] Consumption pattern analysis

* [ ] **Repository Layer**
  + [ ] `UsageLogRepository` implementation
  + [ ] Database migration for usage_logs table
  + [ ] Analytics query methods

* [ ] **Use Cases**
  + [ ] `LogItemUsage` use case
  + [ ] `GetUsageHistory` use case
  + [ ] `AnalyzeConsumptionPatterns` use case
  + [ ] `PredictUsage` use case

* [ ] **HTTP API**
  + [ ] Usage logging endpoints
  + [ ] Usage history retrieval
  + [ ] Analytics endpoints

---

## 🟡 **MEDIUM PRIORITY - Enhanced Features**

### 5. Inventory Adjustments ❌

**Status:** Not Started  
**Priority:** Medium  
**Estimated Effort:** 2 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `InventoryAdjustment` entity
  + [ ] Adjustment types (spoilage, loss, manual_correction, transfer)
  + [ ] Adjustment validation logic

* [ ] **Repository Layer**
  + [ ] `InventoryAdjustmentRepository` implementation
  + [ ] Database migration for inventory_adjustments table

* [ ] **Use Cases**
  + [ ] `RecordAdjustment` use case
  + [ ] `GetAdjustmentHistory` use case
  + [ ] `RecordSpoilage` use case

* [ ] **HTTP API**
  + [ ] Adjustment recording endpoints
  + [ ] Adjustment history endpoints

### 6. Account Recovery System ❌

**Status:** Not Started  
**Priority:** Medium  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Domain Models**
  + [ ] `PasswordResetToken` entity
  + [ ] Token generation and validation logic

* [ ] **Repository Layer**
  + [ ] `PasswordResetTokenRepository` implementation
  + [ ] Database migration for password reset tokens

* [ ] **Use Cases**
  + [ ] `RequestPasswordReset` use case
  + [ ] `ValidateResetToken` use case
  + [ ] `ResetPassword` use case

* [ ] **HTTP API**
  + [ ] Password reset request endpoint
  + [ ] Password reset confirmation endpoint

* [ ] **Email Integration**
  + [ ] Email service for reset links
  + [ ] Email templates

### 7. Idempotency Middleware Integration ❌

**Status:** Service Exists, Middleware Missing  
**Priority:** Medium  
**Estimated Effort:** 1 day

#### **Missing Components:**

* [ ] **Middleware Implementation**
  + [ ] Fiber middleware for idempotency
  + [ ] Integration with existing `IdempotencyService`
  + [ ] Header validation and processing

* [ ] **Configuration**
  + [ ] Idempotency configuration settings
  + [ ] TTL and cache settings

#### **Current Status:**

```go
// Service exists: internal/infra/redis/idempotency_service.go
// Missing: Middleware integration in HTTP layer
```

---

## 🟢 **LOW PRIORITY - Future Enhancements**

### 8. Advanced Notification Channels ❌

**Status:** Architecture Ready, Providers Not Implemented  
**Priority:** Low  
**Estimated Effort:** 3-5 days

#### **Missing Components:**

* [ ] **Email Provider**
  + [ ] SMTP configuration
  + [ ] Email templates
  + [ ] HTML/text email generation

* [ ] **SMS Provider** (Future)
  + [ ] SMS service integration
  + [ ] SMS templates

* [ ] **Push Notification Provider** (Future)
  + [ ] Push notification service
  + [ ] Device token management

#### **Current Status:**

```go
// Architecture exists: internal/infra/notification/providers/
// All providers have TODO placeholders
```

### 9. Casbin Authorization Migration ❌

**Status:** Custom System Works, Migration Optional  
**Priority:** Low  
**Estimated Effort:** 2-3 days

#### **Missing Components:**

* [ ] **Casbin Integration**
  + [ ] Casbin enforcer setup
  + [ ] Policy management
  + [ ] Migration from current authorization system

* [ ] **Policy Management**
  + [ ] Dynamic policy updates
  + [ ] Role-based policy definitions

#### **Current Status:**

```go
// Working alternative: internal/infra/auth/pantry_authorization_service.go
// Requirements specify Casbin but current system is functional
```

---

## 📋 **Implementation Checklist by Component**

### **Domain Layer Completeness**

* [x] User management
* [x] Pantry management  
* [x] Product catalog
* [x] Inventory tracking
* [x] Shopping lists
* [x] Recipe management
* [x] Notifications
* [ ] Enhanced shopping lists (receipt/store features)
* [ ] Usage tracking
* [ ] Inventory adjustments
* [ ] Pantry settings

### **Repository Layer Completeness**

* [x] User repositories
* [x] Pantry repositories
* [x] Product repositories
* [x] Inventory repositories
* [x] Shopping list repositories
* [x] Recipe repositories
* [x] Notification repositories
* [ ] Enhanced shopping list repositories (receipt/store features)
* [ ] Usage log repositories
* [ ] Adjustment repositories
* [ ] Settings repositories

### **Use Case Layer Completeness**

* [x] Authentication use cases
* [x] User management use cases (including password change)
* [x] Pantry management use cases
* [x] Inventory use cases
* [x] Shopping list use cases
* [x] Recipe use cases
* [x] Expiration tracking use cases
* [ ] Enhanced shopping list use cases (receipt/store features)
* [ ] Usage analytics use cases
* [ ] Settings management use cases

### **HTTP API Completeness**

* [x] Authentication endpoints
* [x] User management endpoints (including password change)
* [x] Pantry management endpoints (including membership system)
* [x] Product catalog endpoints
* [x] Inventory endpoints
* [x] Shopping list endpoints
* [x] Recipe endpoints
* [x] Expiration tracking endpoints
* [ ] Enhanced shopping list endpoints (receipt/store features)
* [ ] Usage tracking endpoints
* [ ] Settings management endpoints

---

## 🎯 **Next Sprint Recommendations**

### **Sprint 1: Core MVP Completion (1-2 weeks)**

1. Enhanced Shopping Lists (receipt/store features) (2-3 days)
2. Pantry-Specific Settings (2-3 days)
3. ~~Password Change Functionality (1 day)~~ ✅ **COMPLETED**

### **Sprint 2: Enhanced Features (1 week)**

1. Usage Tracking & Analytics (2-3 days)
2. Inventory Adjustments (2 days)
3. Idempotency Middleware (1 day)

### **Sprint 3: Security & Recovery (1 week)**

1. Account Recovery System (2-3 days)
2. Advanced notification channels (3-5 days)

---

## 📈 **Progress Tracking**

**Last Updated:** June 12, 2025
**Total Features:** 20
**Completed:** 16 ✅
**In Progress:** 1 🚧
**Not Started:** 3 ❌

**Completion Rate:** 80% ✅

## 🔧 **TODO Resolution Completed**

**Original TODO Items Found:** 15 items across multiple files
**Resolved:** 13 items ✅
**Remaining:** 2 items (complex features requiring external services)

### ✅ **Resolved TODOs:**

1. **Simple Comment Updates** (3 items)
   - Updated pantry membership handler comment to be descriptive
   - Fixed expiration handler authentication with proper middleware usage
   - Updated conversion factor endpoint documentation

2. **Test Infrastructure Fixes** (2 items)
   - **MAJOR**: Fixed and enabled ExpirationTracking test (was commented out)
   - Fixed alert configuration JSON serialization issues
   - Updated test request structures to match domain models

3. **Business Logic Implementation** (6 items)
   - Added location name lookup in expiration tracking (with pantry location repository)
   - Implemented global user alerts across all pantries
   - Enhanced product details fetching in inventory use case
   - Added basic template rendering with placeholder replacement
   - Updated consumption rate calculation with detailed implementation notes
   - Enhanced price estimation with comprehensive documentation

4. **Dependency Injection Updates** (2 items)
   - Added product repository to inventory use case
   - Added pantry location repository to expiration use case

### 🚧 **Remaining TODOs (Complex External Integrations):**

1. **Unit Conversion System** - Recipe ingredient availability checking
   - Requires advanced unit conversion logic and compatibility validation
   - Would need conversion factors table and complex business rules

2. **Notification Provider Implementations** - Email, Telegram, Supabase, Webhook
   - Requires external service configurations and API integrations
   - Updated with comprehensive implementation guides and requirements

---

## 🛠️ **Detailed Implementation Guides**

### **Enhanced Shopping Lists Implementation Guide**

#### **Step 1: Domain Model Enhancements**

```go
// internal/core/domain/shopping_list.go - Add to existing ShoppingList struct
type ShoppingList struct {
    // ... existing fields ...
    ReceiptImageURL *string    `json:"receipt_image_url,omitempty"`
    StoreName       *string    `json:"store_name,omitempty"`
    StoreAddress    *string    `json:"store_address,omitempty"`
    TotalAmount     *float64   `json:"total_amount,omitempty"`
    CompletedAt     *time.Time `json:"completed_at,omitempty"`
}

// Enhance ShoppingListItemEntity with price tracking
type ShoppingListItemEntity struct {
    // ... existing fields ...
    ActualPrice     *float64   `json:"actual_price,omitempty"`
    EstimatedPrice  *float64   `json:"estimated_price,omitempty"`
}
```

#### **Step 2: New Use Cases**

```go
// internal/core/usecases/shopping_list_service.go - Add methods
func (s *ShoppingListService) CompleteShoppingList(ctx context.Context, req CompleteShoppingListRequest) (*domain.ShoppingList, error) {
    // 1. Mark shopping list as completed
    // 2. Store receipt and store information
    // 3. Calculate total amount from purchased items
    // 4. Optionally create inventory items from purchased items
}

func (s *ShoppingListService) GetPurchaseHistory(ctx context.Context, userID, pantryID uuid.UUID) ([]*domain.ShoppingList, error) {
    // Return completed shopping lists as purchase history
}
```

#### **Step 3: API Enhancements**

```go
// internal/infra/web/handler/shopping_list_handler.go - Add endpoints
func (h *ShoppingListHandler) CompleteShoppingList(c *fiber.Ctx) error
func (h *ShoppingListHandler) UploadReceipt(c *fiber.Ctx) error
func (h *ShoppingListHandler) GetPurchaseHistory(c *fiber.Ctx) error
func (h *ShoppingListHandler) GetSpendingAnalytics(c *fiber.Ctx) error
```

### **Pantry Settings Implementation Guide**

#### **Step 1: Domain Model**

```go
// internal/core/domain/pantry_settings.go
type PantrySettings struct {
    ID                    uuid.UUID
    PantryID              uuid.UUID
    LowStockThresholds    map[uuid.UUID]float64 // ProductVariantID -> Threshold
    DefaultLocationID     *uuid.UUID
    PreferredCurrency     string
    PreferredUnits        map[string]uuid.UUID // UnitType -> UnitID
    NotificationSettings  NotificationSettings
    UpdatedByUserID       uuid.UUID
    CreatedAt            time.Time
    UpdatedAt            time.Time
}

type NotificationSettings struct {
    LowStockEnabled     bool
    ExpirationEnabled   bool
    EmailNotifications  bool
    InAppNotifications  bool
}
```

#### **Step 2: Use Cases**

```go
// internal/core/usecases/pantry_settings_usecase.go
func (uc *PantrySettingsUsecase) UpdateSettings(ctx context.Context, userID, pantryID uuid.UUID, req *UpdateSettingsRequest) (*domain.PantrySettings, error) {
    // 1. Check edit pantry permission
    // 2. Get or create settings
    // 3. Update settings
    // 4. Validate settings
    // 5. Save to repository
}
```

### **Password Change Implementation Guide** ✅

**Status:** COMPLETED - Password change functionality is fully implemented with security validation, token revocation, and event logging.

---

## 📝 **Database Migration Scripts Needed**

### **Enhanced Shopping Lists Tables**

```sql
-- migrations/000015_enhance_shopping_lists.up.sql
-- Add receipt and store information to existing shopping_lists table
ALTER TABLE shopping_lists ADD COLUMN receipt_image_url VARCHAR(500);
ALTER TABLE shopping_lists ADD COLUMN store_name VARCHAR(255);
ALTER TABLE shopping_lists ADD COLUMN store_address VARCHAR(500);
ALTER TABLE shopping_lists ADD COLUMN total_amount DECIMAL(10, 2);
ALTER TABLE shopping_lists ADD COLUMN completed_at TIMESTAMPTZ;

-- Add price tracking to existing shopping_list_items table
ALTER TABLE shopping_list_items ADD COLUMN actual_price DECIMAL(10, 2);
ALTER TABLE shopping_list_items ADD COLUMN estimated_price DECIMAL(10, 2);

-- Create index for purchase history queries
CREATE INDEX idx_shopping_lists_completed_at ON shopping_lists(completed_at) WHERE completed_at IS NOT NULL;
CREATE INDEX idx_shopping_lists_pantry_completed ON shopping_lists(pantry_id, completed_at) WHERE completed_at IS NOT NULL;
```

### **Settings Table**

```sql
-- migrations/000016_create_pantry_settings_table.up.sql
CREATE TABLE pantry_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    pantry_id UUID NOT NULL REFERENCES pantries(id) ON DELETE CASCADE,
    setting_key VARCHAR(100) NOT NULL,
    setting_value JSONB,
    data_type VARCHAR(20) NOT NULL,
    updated_by_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (pantry_id, setting_key)
);
```

### **Usage Logs Table**

```sql
-- migrations/000017_create_usage_logs_table.up.sql
CREATE TABLE usage_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    inventory_item_id UUID NOT NULL REFERENCES inventory_items(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
    quantity_used DECIMAL(10, 3) NOT NULL,
    unit_of_measure_id UUID NOT NULL REFERENCES unit_of_measures(id) ON DELETE RESTRICT,
    usage_date TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

---

## 🔄 **Integration Points**

### **Enhanced Shopping Lists → Inventory Integration**

* Link completed shopping list items to `InventoryItem` creation
* Auto-populate purchase price and date from shopping list completion
* Track purchase history through completed shopping lists

### **Settings → Inventory Integration**

* Use low stock thresholds in shopping list generation
* Apply default locations when adding inventory
* Use preferred units in quantity conversions

### **Usage Logs → Analytics Integration**

* Feed consumption data to shopping list algorithms
* Provide usage patterns for expiration predictions
* Support inventory forecasting features

---

## 🎯 **Success Criteria**

### **Enhanced Shopping Lists**

* [ ] Can store receipt images with completed shopping lists
* [ ] Can track store information for shopping trips
* [ ] Can link completed shopping items to inventory
* [ ] Can provide purchase history through completed lists
* [ ] Can analyze spending patterns and costs

### **Pantry Settings**

* [ ] Can configure low stock thresholds
* [ ] Can set default locations and units
* [ ] Settings are applied throughout the system
* [ ] Settings are pantry-specific

### **Password Change**

* [ ] Secure current password verification
* [ ] Strong password validation
* [ ] Automatic token revocation
* [ ] Security event logging

### **Usage Tracking**

* [ ] Detailed consumption logging
* [ ] Usage history retrieval
* [ ] Consumption pattern analysis
* [ ] Integration with shopping list generation
