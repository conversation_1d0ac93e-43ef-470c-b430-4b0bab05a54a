package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/wongpinter/pantry-pal/internal/test/testutil"
)

// CategoryTestSuite tests category management endpoints
type CategoryTestSuite struct {
	IntegrationTestSuite
}

// TestCategoryEndpoints tests all category endpoints
func (s *CategoryTestSuite) TestCategoryEndpoints() {
	s.Run("CRUD", s.testCategoryCRUD)
	s.Run("Hierarchy", s.testCategoryHierarchy)
	s.Run("Validation", s.testCategoryValidation)
}

func (s *CategoryTestSuite) testCategoryCRUD() {
	s.RequireAuth()

	config := CRUDTestConfig{
		CreatePath: "/api/v1/catalog/categories",
		GetPath:    "/api/v1/catalog/categories/%s",
		ListPath:   "/api/v1/catalog/categories",
		UpdatePath: "/api/v1/catalog/categories/%s",
		DeletePath: "/api/v1/catalog/categories/%s",
		CreatePayload: map[string]interface{}{
			"name":        "Test Category",
			"description": "A test category for integration testing",
		},
		UpdatePayload: map[string]interface{}{
			"name":        "Updated Test Category",
			"description": "An updated test category description",
		},
	}

	s.RunCRUDTests(config)
}

func (s *CategoryTestSuite) testCategoryHierarchy() {
	s.RequireAuth()

	// Create parent category
	s.Run("CreateParentCategory", func() {
		parentReq := map[string]interface{}{
			"name":        "Parent Category",
			"description": "Parent category for hierarchy testing",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", parentReq)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
		parentID := testutil.ExtractIDFromResponse(s.T(), successResp)

		// Create child category
		s.Run("CreateChildCategory", func() {
			childReq := map[string]interface{}{
				"name":               "Child Category",
				"description":        "Child category for hierarchy testing",
				"parent_category_id": parentID,
			}

			resp, err := s.http.POST("/api/v1/catalog/categories", childReq)
			s.Require().NoError(err)
			successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
			childID := testutil.ExtractIDFromResponse(s.T(), successResp)

			// Test getting subcategories
			s.Run("GetSubcategories", func() {
				path := testutil.BuildPath("/api/v1/catalog/categories/%s/subcategories", parentID)
				resp, err := s.http.GET(path)
				s.Require().NoError(err)

				successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

				// The API returns data directly as an array, not wrapped in an object
				items, ok := successResp["data"].([]interface{})
				s.Require().True(ok)
				s.GreaterOrEqual(len(items), 1, "Should have at least one subcategory")

				// Verify child category is in the list
				found := false
				for _, item := range items {
					category := item.(map[string]interface{})
					if category["id"].(string) == childID {
						found = true
						s.Equal("Child Category", category["name"])
						break
					}
				}
				s.True(found, "Child category should be in subcategories list")
			})

			// Test moving category
			s.Run("MoveCategory", func() {
				// Create another parent
				newParentReq := map[string]interface{}{
					"name":        "New Parent Category",
					"description": "New parent for move testing",
				}

				resp, err := s.http.POST("/api/v1/catalog/categories", newParentReq)
				s.Require().NoError(err)
				successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
				newParentID := testutil.ExtractIDFromResponse(s.T(), successResp)

				// Move child to new parent
				moveReq := map[string]interface{}{
					"parent_category_id": newParentID,
				}

				path := testutil.BuildPath("/api/v1/catalog/categories/%s/move", childID)
				resp, err = s.http.POST(path, moveReq)
				s.Require().NoError(err)
				s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

				// Verify the move
				path = testutil.BuildPath("/api/v1/catalog/categories/%s", childID)
				resp, err = s.http.GET(path)
				s.Require().NoError(err)
				successResp = s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

				data, ok := successResp["data"].(map[string]interface{})
				s.Require().True(ok)
				s.Equal(newParentID, data["parent_category_id"])
			})
		})
	})
}

func (s *CategoryTestSuite) testCategoryValidation() {
	s.RequireAuth()

	// Test missing required fields
	s.Run("MissingName", func() {
		req := map[string]interface{}{
			"description": "Category without name",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})

	// Test empty name
	s.Run("EmptyName", func() {
		req := map[string]interface{}{
			"name":        "",
			"description": "Category with empty name",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})

	// Test invalid parent category ID
	s.Run("InvalidParentID", func() {
		req := map[string]interface{}{
			"name":               "Test Category",
			"description":        "Category with invalid parent",
			"parent_category_id": "invalid-uuid",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertErrorResponse(s.T(), resp, http.StatusBadRequest)
	})

	// Test non-existent parent category ID
	s.Run("NonExistentParentID", func() {
		req := map[string]interface{}{
			"name":               "Test Category",
			"description":        "Category with non-existent parent",
			"parent_category_id": "550e8400-e29b-41d4-a716-************",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertErrorResponse(s.T(), resp, http.StatusNotFound)
	})

	// Test duplicate category name at same level
	s.Run("DuplicateName", func() {
		// Create first category
		req := map[string]interface{}{
			"name":        "Duplicate Name",
			"description": "First category with this name",
		}

		resp, err := s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)

		// Try to create second category with same name
		req["description"] = "Second category with same name"
		resp, err = s.http.POST("/api/v1/catalog/categories", req)
		s.Require().NoError(err)
		s.http.AssertErrorResponse(s.T(), resp, http.StatusConflict)
	})
}

// TestCategorySearch tests category search functionality
func (s *CategoryTestSuite) TestCategorySearch() {
	s.RequireAuth()

	// Create test categories
	categories := []map[string]interface{}{
		{"name": "Fruits", "description": "Fresh fruits"},
		{"name": "Vegetables", "description": "Fresh vegetables"},
		{"name": "Dairy", "description": "Dairy products"},
		{"name": "Frozen Foods", "description": "Frozen food items"},
	}

	for _, category := range categories {
		resp, err := s.http.POST("/api/v1/catalog/categories", category)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	}

	// Test search by name
	s.Run("SearchByName", func() {
		resp, err := s.http.GET("/api/v1/catalog/categories?search=Fruit")
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

		// The API returns data directly as an array, not wrapped in an object
		items, ok := successResp["data"].([]interface{})
		s.Require().True(ok)
		s.GreaterOrEqual(len(items), 1, "Should find at least one category")

		// Verify search results contain "Fruits"
		found := false
		for _, item := range items {
			category := item.(map[string]interface{})
			if category["name"].(string) == "Fruits" {
				found = true
				break
			}
		}
		s.True(found, "Should find 'Fruits' category")
	})

	// Test pagination (if implemented)
	s.Run("Pagination", func() {
		resp, err := s.http.GET("/api/v1/catalog/categories?page=1&limit=2")
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

		// The API returns data directly as an array, not wrapped in an object
		items, ok := successResp["data"].([]interface{})
		s.Require().True(ok)

		// Note: If pagination is not implemented, this will return all items
		// For now, just verify we get some results
		s.GreaterOrEqual(len(items), 1, "Should return at least one category")
	})
}

// TestCategoryTestSuite runs the category test suite
func TestCategoryTestSuite(t *testing.T) {
	suite.Run(t, new(CategoryTestSuite))
}
