package integration

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/wongpinter/pantry-pal/internal/test/testutil"
)

// PantryTestSuite tests pantry management endpoints
type PantryTestSuite struct {
	IntegrationTestSuite
}

// TestPantryEndpoints tests all pantry endpoints
func (s *PantryTestSuite) TestPantryEndpoints() {
	s.Run("CRUD", s.testPantryCRUD)
	s.Run("Ownership", s.testPantryOwnership)
	s.Run("Validation", s.testPantryValidation)
}

func (s *PantryTestSuite) testPantryCRUD() {
	s.RequireAuth()

	config := CRUDTestConfig{
		CreatePath: "/api/v1/pantries",
		GetPath:    "/api/v1/pantries/%s",
		ListPath:   "/api/v1/pantries",
		UpdatePath: "/api/v1/pantries/%s",
		DeletePath: "/api/v1/pantries/%s",
		CreatePayload: map[string]interface{}{
			"name":        "Test Pantry",
			"description": "A test pantry for integration testing",
		},
		UpdatePayload: map[string]interface{}{
			"name":        "Updated Test Pantry",
			"description": "An updated test pantry description",
		},
	}

	s.RunCRUDTests(config)
}

func (s *PantryTestSuite) testPantryOwnership() {
	s.RequireAuth()

	// Create a pantry
	s.Run("CreatePantry", func() {
		pantryReq := map[string]interface{}{
			"name":        "Ownership Test Pantry",
			"description": "Pantry for ownership testing",
		}

		resp, err := s.http.POST("/api/v1/pantries", pantryReq)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
		pantryID := testutil.ExtractIDFromResponse(s.T(), successResp)

		// Create another user
		s.Run("TransferOwnership", func() {
			// Register new user
			newUserData := s.http.RegisterTestUser(s.T(), "newowner", "<EMAIL>", "NewOwnerPass123!")
			newUserID := newUserData["id"].(string)

			// Transfer ownership
			transferReq := map[string]interface{}{
				"new_owner_user_id": newUserID,
			}

			path := testutil.BuildPath("/api/v1/pantries/%s/transfer-ownership", pantryID)
			resp, err := s.http.POST(path, transferReq)
			s.Require().NoError(err)
			s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

			// Verify ownership transfer
			path = testutil.BuildPath("/api/v1/pantries/%s", pantryID)
			resp, err = s.http.GET(path)
			s.Require().NoError(err)
			successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

			data, ok := successResp["data"].(map[string]interface{})
			s.Require().True(ok)
			s.Equal(newUserID, data["owner_user_id"])
		})
	})
}

func (s *PantryTestSuite) testPantryValidation() {
	s.RequireAuth()

	// Test missing required fields
	s.Run("MissingName", func() {
		req := map[string]interface{}{
			"description": "Pantry without name",
		}

		resp, err := s.http.POST("/api/v1/pantries", req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})

	// Test empty name
	s.Run("EmptyName", func() {
		req := map[string]interface{}{
			"name":        "",
			"description": "Pantry with empty name",
		}

		resp, err := s.http.POST("/api/v1/pantries", req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})

	// Test name too long
	s.Run("NameTooLong", func() {
		longName := make([]byte, 256)
		for i := range longName {
			longName[i] = 'a'
		}

		req := map[string]interface{}{
			"name":        string(longName),
			"description": "Pantry with very long name",
		}

		resp, err := s.http.POST("/api/v1/pantries", req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})
}

// TestPantryLocations tests pantry location management
func (s *PantryTestSuite) TestPantryLocations() {
	s.RequireAuth()

	// Create a pantry first
	pantryReq := map[string]interface{}{
		"name":        "Location Test Pantry",
		"description": "Pantry for location testing",
	}

	resp, err := s.http.POST("/api/v1/pantries", pantryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	pantryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Test location CRUD
	s.Run("LocationCRUD", func() {
		config := CRUDTestConfig{
			CreatePath: testutil.BuildPath("/api/v1/pantries/%s/locations", pantryID),
			GetPath:    testutil.BuildPath("/api/v1/pantries/%s/locations/%%s", pantryID),
			ListPath:   testutil.BuildPath("/api/v1/pantries/%s/locations", pantryID),
			UpdatePath: testutil.BuildPath("/api/v1/pantries/%s/locations/%%s", pantryID),
			DeletePath: testutil.BuildPath("/api/v1/pantries/%s/locations/%%s", pantryID),
			CreatePayload: map[string]interface{}{
				"name":        "Kitchen Cabinet",
				"description": "Main kitchen storage cabinet",
			},
			UpdatePayload: map[string]interface{}{
				"name":        "Updated Kitchen Cabinet",
				"description": "Updated kitchen storage description",
			},
		}

		s.RunCRUDTests(config)
	})

	// Test location validation
	s.Run("LocationValidation", func() {
		// Test missing name
		req := map[string]interface{}{
			"description": "Location without name",
		}

		path := testutil.BuildPath("/api/v1/pantries/%s/locations", pantryID)
		resp, err := s.http.POST(path, req)
		s.Require().NoError(err)
		s.http.AssertValidationError(s.T(), resp, "name")
	})
}

// TestPantryMembership tests pantry membership management
func (s *PantryTestSuite) TestPantryMembership() {
	s.RequireAuth()

	// Create a pantry
	pantryReq := map[string]interface{}{
		"name":        "Membership Test Pantry",
		"description": "Pantry for membership testing",
	}

	resp, err := s.http.POST("/api/v1/pantries", pantryReq)
	s.Require().NoError(err)
	successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	pantryID := testutil.ExtractIDFromResponse(s.T(), successResp)

	// Create another user to invite
	newUserData := s.http.RegisterTestUser(s.T(), "inviteduser", "<EMAIL>", "InvitedPass123!")
	newUserID := newUserData["id"].(string)

	// Test member invitation
	s.Run("InviteMember", func() {
		inviteReq := map[string]interface{}{
			"user_id": newUserID,
			"role":    "member",
		}

		path := testutil.BuildPath("/api/v1/pantries/%s/members", pantryID)
		resp, err := s.http.POST(path, inviteReq)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, http.StatusCreated)
	})

	// Test getting members
	s.Run("GetMembers", func() {
		path := testutil.BuildPath("/api/v1/pantries/%s/members", pantryID)
		resp, err := s.http.GET(path)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

		data, ok := successResp["data"].(map[string]interface{})
		s.Require().True(ok)

		items, ok := data["items"].([]interface{})
		s.Require().True(ok)
		s.GreaterOrEqual(len(items), 2, "Should have at least 2 members (owner + invited)")
	})

	// Test updating member role
	s.Run("UpdateMemberRole", func() {
		// First get the membership ID
		path := testutil.BuildPath("/api/v1/pantries/%s/members", pantryID)
		resp, err := s.http.GET(path)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

		data, ok := successResp["data"].(map[string]interface{})
		s.Require().True(ok)

		items, ok := data["items"].([]interface{})
		s.Require().True(ok)

		var membershipID string
		for _, item := range items {
			member := item.(map[string]interface{})
			if member["user_id"].(string) == newUserID {
				membershipID = member["id"].(string)
				break
			}
		}
		s.NotEmpty(membershipID, "Should find membership for invited user")

		// Update role
		updateReq := map[string]interface{}{
			"role": "admin",
		}

		path = testutil.BuildPath("/api/v1/pantries/%s/members/%s", pantryID, membershipID)
		resp, err = s.http.PUT(path, updateReq)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	})

	// Test removing member
	s.Run("RemoveMember", func() {
		// Get membership ID again
		path := testutil.BuildPath("/api/v1/pantries/%s/members", pantryID)
		resp, err := s.http.GET(path)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)

		data, ok := successResp["data"].(map[string]interface{})
		s.Require().True(ok)

		items, ok := data["items"].([]interface{})
		s.Require().True(ok)

		var membershipID string
		for _, item := range items {
			member := item.(map[string]interface{})
			if member["user_id"].(string) == newUserID {
				membershipID = member["id"].(string)
				break
			}
		}
		s.NotEmpty(membershipID, "Should find membership for invited user")

		// Remove member
		path = testutil.BuildPath("/api/v1/pantries/%s/members/%s", pantryID, membershipID)
		resp, err = s.http.DELETE(path)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, http.StatusOK)
	})
}

// TestPantryTestSuite runs the pantry test suite
func TestPantryTestSuite(t *testing.T) {
	suite.Run(t, new(PantryTestSuite))
}
