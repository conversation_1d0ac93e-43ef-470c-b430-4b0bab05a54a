package integration

import (
	"context"
	"strings"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"

	"github.com/wongpinter/pantry-pal/internal/infra/config"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
	"github.com/wongpinter/pantry-pal/internal/infra/web"
	"github.com/wongpinter/pantry-pal/internal/test/testutil"
)

// IntegrationTestSuite is the base test suite for integration tests
type IntegrationTestSuite struct {
	suite.Suite
	ctx       context.Context
	testDB    *testutil.TestDatabase
	app       *fiber.App
	server    *web.Server
	http      *testutil.HTTPTestHelper
	seeds     *testutil.TestSeeds
	authToken string
}

// SetupSuite runs once before all tests in the suite
func (s *IntegrationTestSuite) SetupSuite() {
	s.ctx = context.Background()

	// Setup test database
	testDB, err := testutil.SetupTestDatabase(s.ctx)
	s.Require().NoError(err, "Failed to setup test database")
	s.testDB = testDB

	// Create test seeds
	s.seeds = testutil.NewTestSeeds(testDB.DB)

	// Setup test configuration
	cfg := &config.Config{
		App: config.AppConfig{
			Name:        "pantry-pal-test",
			Version:     "test",
			Environment: "test",
		},
		Server: config.ServerConfig{
			Host: "localhost",
			Port: "8080",
			CORS: config.CORSConfig{
				AllowOrigins:     []string{"http://localhost:3000", "http://localhost:8080"},
				AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
				AllowHeaders:     []string{"*"},
				AllowCredentials: true,
			},
		},
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     "5432",
			Username: "test",
			Password: "test",
			Database: "pantrypal_test",
			SSLMode:  "disable",
		},
		Auth: config.AuthConfig{
			JWT: config.JWTConfig{
				Secret:                 "test-secret-key-for-testing-only",
				AccessTokenExpiration:  "15m",
				RefreshTokenExpiration: "168h", // 7 days in hours
				Issuer:                 "pantry-pal-test",
			},
		},
	}

	// Create logger
	log := logger.New(config.LoggerConfig{
		Level:  "error", // Reduce log noise during tests
		Format: "json",
	})

	// Create server with test database
	server, err := web.NewServerWithDB(cfg, log, testDB.DB)
	s.Require().NoError(err, "Failed to create test server")
	s.server = server
	s.app = server.GetApp()

	// Setup HTTP test helper
	s.http = testutil.NewHTTPTestHelper(s.app)
}

// TearDownSuite runs once after all tests in the suite
func (s *IntegrationTestSuite) TearDownSuite() {
	if s.testDB != nil {
		s.testDB.Cleanup(s.ctx)
	}
}

// SetupTest runs before each test
func (s *IntegrationTestSuite) SetupTest() {
	// Clean database before each test
	err := s.testDB.CleanupData()
	s.Require().NoError(err, "Failed to cleanup test data")

	// Reset auth token
	s.authToken = ""
	s.http.SetAuthToken("")
}

// TearDownTest runs after each test
func (s *IntegrationTestSuite) TearDownTest() {
	// Additional cleanup if needed
}

// CreateAuthenticatedUser creates a test user and authenticates
func (s *IntegrationTestSuite) CreateAuthenticatedUser() (string, map[string]interface{}) {
	// Generate unique credentials for each test to avoid conflicts
	testID := uuid.New().String()[:8]
	// Remove hyphens to make it alphanumeric only
	testID = strings.ReplaceAll(testID, "-", "")
	username := "testuser" + testID
	email := "test" + testID + "@example.com"
	password := "TestPassword123!"

	token, userData := s.http.CreateAuthenticatedUser(s.T(), username, email, password)
	s.authToken = token
	return token, userData
}

// GetAuthToken returns the current auth token
func (s *IntegrationTestSuite) GetAuthToken() string {
	if s.authToken == "" {
		s.authToken, _ = s.CreateAuthenticatedUser()
	}
	return s.authToken
}

// RequireAuth ensures the test has an authenticated user
func (s *IntegrationTestSuite) RequireAuth() string {
	return s.GetAuthToken()
}

// TestHealthEndpoint tests the health check endpoint
func (s *IntegrationTestSuite) TestHealthEndpoint() {
	resp, err := s.http.GET("/health")
	s.Require().NoError(err)
	s.http.AssertStatusCode(s.T(), resp, 200)

	var healthResp map[string]interface{}
	s.http.AssertJSONResponse(s.T(), resp, &healthResp)
	s.Equal("ok", healthResp["status"])
}

// RunCRUDTests runs standard CRUD tests for an entity
func (s *IntegrationTestSuite) RunCRUDTests(config CRUDTestConfig) {
	s.Run("Create", func() {
		s.RequireAuth()
		resp, err := s.http.POST(config.CreatePath, config.CreatePayload)
		s.Require().NoError(err)
		successResp := s.http.AssertSuccessResponse(s.T(), resp, 201)

		// Store created ID for other tests
		config.CreatedID = testutil.ExtractIDFromResponse(s.T(), successResp)
		s.NotEmpty(config.CreatedID)
	})

	s.Run("Get", func() {
		s.RequireAuth()
		s.Require().NotEmpty(config.CreatedID, "Create test must run first")

		path := testutil.BuildPath(config.GetPath, config.CreatedID)
		resp, err := s.http.GET(path)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, 200)
	})

	s.Run("List", func() {
		s.RequireAuth()
		resp, err := s.http.GET(config.ListPath)
		s.Require().NoError(err)

		successResp := s.http.AssertSuccessResponse(s.T(), resp, 200)

		// The API returns data directly as an array, not wrapped in an object
		items, ok := successResp["data"].([]interface{})
		s.Require().True(ok)
		s.GreaterOrEqual(len(items), 1, "Should have at least one item")
	})

	s.Run("Update", func() {
		s.RequireAuth()
		s.Require().NotEmpty(config.CreatedID, "Create test must run first")

		path := testutil.BuildPath(config.UpdatePath, config.CreatedID)
		resp, err := s.http.PUT(path, config.UpdatePayload)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, 200)
	})

	s.Run("Delete", func() {
		s.RequireAuth()
		s.Require().NotEmpty(config.CreatedID, "Create test must run first")

		path := testutil.BuildPath(config.DeletePath, config.CreatedID)
		resp, err := s.http.DELETE(path)
		s.Require().NoError(err)
		s.http.AssertSuccessResponse(s.T(), resp, 200)
	})
}

// CRUDTestConfig contains configuration for CRUD tests
type CRUDTestConfig struct {
	CreatePath    string
	GetPath       string
	ListPath      string
	UpdatePath    string
	DeletePath    string
	CreatePayload interface{}
	UpdatePayload interface{}
	CreatedID     string
}

// RunTestSuite runs the integration test suite
func RunTestSuite(t *testing.T, testSuite suite.TestingSuite) {
	suite.Run(t, testSuite)
}
