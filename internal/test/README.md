# Pantry Pal Testing Framework

This directory contains a comprehensive testing framework for the Pantry Pal application, designed to test all endpoint handlers with proper database isolation and cleanup.

## Overview

The testing framework provides:

- **Database Isolation**: Each test runs with a fresh PostgreSQL container using testcontainers-go
- **Test Seeds**: Factory methods to create test data for all domain entities
- **HTTP Testing Utilities**: Helper functions for making HTTP requests and assertions
- **CRUD Test Patterns**: Reusable test patterns for standard CRUD operations
- **Comprehensive Coverage**: Tests for all handlers including auth, categories, pantries, inventory, etc.

## Structure

```
internal/test/
├── README.md                    # This file
├── testutil/                    # Test utilities and helpers
│   ├── database.go             # Database setup and cleanup
│   ├── seeds.go                # Test data factory methods
│   └── http.go                 # HTTP testing utilities
└── integration/                # Integration test suites
    ├── suite.go                # Base test suite
    ├── auth_test.go            # Authentication endpoint tests
    ├── category_test.go        # Category management tests
    ├── pantry_test.go          # Pantry management tests
    └── all_test.go             # Comprehensive endpoint tests
```

## Prerequisites

1. **Docker**: Required for running PostgreSQL test containers
2. **Go 1.21+**: For running the tests
3. **Dependencies**: All required Go modules (automatically installed)

## Running Tests

### Using Make Commands

```bash
# Run all tests (unit + integration)
make test

# Run only unit tests
make test-unit

# Run only integration tests
make test-integration

# Run comprehensive endpoint tests
make test-endpoints

# Run specific handler tests
make test-auth
make test-category
make test-pantry

# Run tests with coverage
make test-coverage
```

### Using the Test Runner Script

```bash
# Run all test suites
./scripts/run-integration-tests.sh

# Run specific test suite
./scripts/run-integration-tests.sh auth
./scripts/run-integration-tests.sh categories
./scripts/run-integration-tests.sh pantries
./scripts/run-integration-tests.sh all

# Run unit tests only
./scripts/run-integration-tests.sh unit

# Generate coverage report
./scripts/run-integration-tests.sh coverage

# Show help
./scripts/run-integration-tests.sh help
```

### Using Go Test Directly

```bash
# Run all integration tests
go test -v ./internal/test/integration/... -timeout=10m

# Run specific test suite
go test -v ./internal/test/integration/... -run TestAuthTestSuite
go test -v ./internal/test/integration/... -run TestCategoryTestSuite
go test -v ./internal/test/integration/... -run TestPantryTestSuite

# Run comprehensive endpoint tests
go test -v ./internal/test/integration/... -run TestAllEndpointsTestSuite -timeout=15m
```

## Test Features

### Database Management

- **Isolated Containers**: Each test suite gets a fresh PostgreSQL container
- **Auto Migration**: Database schema is automatically migrated using GORM
- **Data Cleanup**: All data is cleaned between individual tests
- **Transaction Support**: Tests can use database transactions for complex scenarios

### Test Data Factory

The `TestSeeds` struct provides factory methods for creating test data:

```go
seeds := testutil.NewTestSeeds(db)

// Create individual entities
user, err := seeds.CreateTestUser("username", "<EMAIL>")
category, err := seeds.CreateTestCategory("Category Name", nil)
pantry, err := seeds.CreateTestPantry("Pantry Name", userID)

// Create complete related dataset
testData, err := seeds.CreateCompleteTestData()
// Returns: User, Category, Unit, Product, Variant, Pantry, Location, Inventory, Recipe
```

### HTTP Testing Utilities

The `HTTPTestHelper` provides convenient methods for testing HTTP endpoints:

```go
http := testutil.NewHTTPTestHelper(app)

// Authentication
token := http.LoginAndGetToken(t, email, password)
userData := http.RegisterTestUser(t, username, email, password)

// HTTP Methods
resp, err := http.GET("/api/v1/endpoint")
resp, err := http.POST("/api/v1/endpoint", payload)
resp, err := http.PUT("/api/v1/endpoint", payload)
resp, err := http.DELETE("/api/v1/endpoint")

// Assertions
http.AssertStatusCode(t, resp, 200)
http.AssertSuccessResponse(t, resp, 201)
http.AssertErrorResponse(t, resp, 400)
http.AssertValidationError(t, resp, "field_name")
```

### CRUD Test Patterns

The framework provides reusable CRUD test patterns:

```go
config := CRUDTestConfig{
    CreatePath: "/api/v1/categories",
    GetPath:    "/api/v1/categories/%s",
    ListPath:   "/api/v1/categories",
    UpdatePath: "/api/v1/categories/%s",
    DeletePath: "/api/v1/categories/%s",
    CreatePayload: map[string]interface{}{
        "name": "Test Category",
    },
    UpdatePayload: map[string]interface{}{
        "name": "Updated Category",
    },
}

s.RunCRUDTests(config)
```

## Test Coverage

The testing framework covers:

### Authentication Endpoints
- User registration with validation
- User login with credential verification
- Token refresh functionality
- User logout

### Category Management
- Category CRUD operations
- Hierarchical category relationships
- Category moving and reorganization
- Validation and error handling

### Pantry Management
- Pantry CRUD operations
- Ownership transfer
- Member invitation and management
- Location management within pantries

### Product Catalog
- Category management
- Unit of measure management
- Product and variant management
- Search and filtering

### Inventory Management
- Inventory item CRUD operations
- Quantity updates and consumption
- Expiration tracking
- Location-based organization

## Best Practices

1. **Test Isolation**: Each test cleans up its data to avoid interference
2. **Realistic Data**: Use the seed factory to create realistic test data
3. **Error Testing**: Test both success and error scenarios
4. **Validation Testing**: Verify input validation and error messages
5. **Authentication**: Most tests require authentication - use `RequireAuth()`
6. **Cleanup**: The framework automatically handles database cleanup

## Troubleshooting

### Docker Issues
- Ensure Docker is running before executing tests
- Check Docker permissions if containers fail to start
- Clean up orphaned containers: `docker ps -a | grep postgres | awk '{print $1}' | xargs docker rm -f`

### Test Timeouts
- Integration tests may take longer due to container startup
- Increase timeout for comprehensive tests: `-timeout=15m`
- Individual test suites typically complete within 5 minutes

### Database Connection Issues
- Verify PostgreSQL container is accessible
- Check for port conflicts (PostgreSQL uses random ports)
- Review container logs: `docker logs <container_id>`

### Memory Issues
- Large test suites may consume significant memory
- Run test suites individually if experiencing memory pressure
- Clean up containers between test runs

## Contributing

When adding new endpoints or handlers:

1. Create test data factory methods in `testutil/seeds.go`
2. Add HTTP test utilities if needed in `testutil/http.go`
3. Create a new test file in `integration/` following the naming pattern
4. Add CRUD tests using the provided patterns
5. Include validation and error scenario tests
6. Update the test runner script to include new test suites

## Performance

- Test suite startup: ~10-30 seconds (container initialization)
- Individual test execution: ~100-500ms per test
- Full test suite: ~5-15 minutes depending on coverage
- Memory usage: ~200-500MB during execution
