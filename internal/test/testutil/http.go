package testutil

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gofiber/fiber/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// HTTPTestHelper provides utilities for HTTP testing
type HTTPTestHelper struct {
	app   *fiber.App
	token string
}

// NewHTTPTestHelper creates a new HTTP test helper
func NewHTTPTestHelper(app *fiber.App) *HTTPTestHelper {
	return &HTTPTestHelper{
		app: app,
	}
}

// SetAuthToken sets the authentication token for requests
func (h *HTTPTestHelper) SetAuthToken(token string) {
	h.token = token
}

// Request makes an HTTP request and returns the response
func (h *HTTPTestHelper) Request(method, path string, body interface{}, headers ...map[string]string) (*http.Response, error) {
	var bodyReader io.Reader

	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req := httptest.NewRequest(method, path, bodyReader)

	// Set default headers
	req.Header.Set("Content-Type", "application/json")

	// Set auth token if available
	if h.token != "" {
		req.Header.Set("Authorization", "Bearer "+h.token)
	}

	// Set additional headers
	for _, headerMap := range headers {
		for key, value := range headerMap {
			req.Header.Set(key, value)
		}
	}

	resp, err := h.app.Test(req, -1) // -1 means no timeout
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	return resp, nil
}

// GET makes a GET request
func (h *HTTPTestHelper) GET(path string, headers ...map[string]string) (*http.Response, error) {
	return h.Request("GET", path, nil, headers...)
}

// POST makes a POST request
func (h *HTTPTestHelper) POST(path string, body interface{}, headers ...map[string]string) (*http.Response, error) {
	return h.Request("POST", path, body, headers...)
}

// PUT makes a PUT request
func (h *HTTPTestHelper) PUT(path string, body interface{}, headers ...map[string]string) (*http.Response, error) {
	return h.Request("PUT", path, body, headers...)
}

// DELETE makes a DELETE request
func (h *HTTPTestHelper) DELETE(path string, headers ...map[string]string) (*http.Response, error) {
	return h.Request("DELETE", path, nil, headers...)
}

// PATCH makes a PATCH request
func (h *HTTPTestHelper) PATCH(path string, body interface{}, headers ...map[string]string) (*http.Response, error) {
	return h.Request("PATCH", path, body, headers...)
}

// AssertStatusCode asserts the response status code
func (h *HTTPTestHelper) AssertStatusCode(t *testing.T, resp *http.Response, expectedStatus int) {
	assert.Equal(t, expectedStatus, resp.StatusCode,
		"Expected status %d, got %d", expectedStatus, resp.StatusCode)
}

// AssertJSONResponse asserts the response is valid JSON and unmarshals it
func (h *HTTPTestHelper) AssertJSONResponse(t *testing.T, resp *http.Response, target interface{}) {
	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err, "Failed to read response body")
	defer resp.Body.Close()

	err = json.Unmarshal(body, target)
	require.NoError(t, err, "Failed to unmarshal JSON response: %s", string(body))
}

// ReadResponseBody reads the response body and returns it as bytes, allowing multiple reads
func (h *HTTPTestHelper) ReadResponseBody(resp *http.Response) ([]byte, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	resp.Body.Close()

	// Create a new ReadCloser with the body content
	resp.Body = io.NopCloser(bytes.NewReader(body))
	return body, nil
}

// AssertErrorResponse asserts the response contains an error
func (h *HTTPTestHelper) AssertErrorResponse(t *testing.T, resp *http.Response, expectedStatus int) map[string]interface{} {
	h.AssertStatusCode(t, resp, expectedStatus)

	var errorResp map[string]interface{}
	h.AssertJSONResponse(t, resp, &errorResp)

	assert.False(t, errorResp["success"].(bool), "Expected success to be false")

	// Check for error message in different possible locations
	if errorObj, ok := errorResp["error"].(map[string]interface{}); ok {
		assert.NotEmpty(t, errorObj["message"], "Expected error message in error object")
	} else if message, ok := errorResp["message"]; ok {
		assert.NotEmpty(t, message, "Expected error message")
	} else {
		t.Error("Expected error message not found in response")
	}

	return errorResp
}

// AssertSuccessResponse asserts the response is successful
func (h *HTTPTestHelper) AssertSuccessResponse(t *testing.T, resp *http.Response, expectedStatus int) map[string]interface{} {
	h.AssertStatusCode(t, resp, expectedStatus)

	var successResp map[string]interface{}
	h.AssertJSONResponse(t, resp, &successResp)

	assert.True(t, successResp["success"].(bool), "Expected success to be true")

	return successResp
}

// LoginAndGetToken performs login and returns the access token
func (h *HTTPTestHelper) LoginAndGetToken(t *testing.T, email, password string) string {
	loginReq := map[string]string{
		"email":    email,
		"password": password,
	}

	resp, err := h.POST("/api/v1/auth/login", loginReq)
	require.NoError(t, err, "Failed to make login request")

	successResp := h.AssertSuccessResponse(t, resp, http.StatusOK)

	data, ok := successResp["data"].(map[string]interface{})
	require.True(t, ok, "Expected data field in response")

	token, ok := data["access_token"].(string)
	require.True(t, ok, "Expected access_token in response")
	require.NotEmpty(t, token, "Expected non-empty access token")

	h.SetAuthToken(token)
	return token
}

// RegisterTestUser registers a test user and returns the user data
func (h *HTTPTestHelper) RegisterTestUser(t *testing.T, username, email, password string) map[string]interface{} {
	registerReq := map[string]string{
		"username":         username,
		"email":            email,
		"password":         password,
		"confirm_password": password,
		"first_name":       "Test",
		"last_name":        "User",
	}

	resp, err := h.POST("/api/v1/auth/register", registerReq)
	require.NoError(t, err, "Failed to make register request")

	successResp := h.AssertSuccessResponse(t, resp, http.StatusCreated)

	data, ok := successResp["data"].(map[string]interface{})
	require.True(t, ok, "Expected data field in response")

	return data
}

// CreateAuthenticatedUser creates a user, logs them in, and returns the token
func (h *HTTPTestHelper) CreateAuthenticatedUser(t *testing.T, username, email, password string) (string, map[string]interface{}) {
	userData := h.RegisterTestUser(t, username, email, password)
	token := h.LoginAndGetToken(t, email, password)
	return token, userData
}

// GetResponseBody reads and returns the response body as string
func (h *HTTPTestHelper) GetResponseBody(resp *http.Response) (string, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	return string(body), nil
}

// PrintResponse prints the response for debugging
func (h *HTTPTestHelper) PrintResponse(t *testing.T, resp *http.Response) {
	body, err := h.ReadResponseBody(resp)
	if err != nil {
		t.Logf("Failed to read response body: %v", err)
		return
	}

	t.Logf("Response Status: %d", resp.StatusCode)
	t.Logf("Response Body: %s", string(body))
}

// AssertValidationError asserts the response contains validation errors
func (h *HTTPTestHelper) AssertValidationError(t *testing.T, resp *http.Response, field string) {
	errorResp := h.AssertErrorResponse(t, resp, http.StatusBadRequest)

	errors, ok := errorResp["errors"].(map[string]interface{})
	if ok {
		assert.Contains(t, errors, field, "Expected validation error for field %s", field)
	}
}

// BuildPath builds a path with parameters
func BuildPath(template string, params ...interface{}) string {
	return fmt.Sprintf(template, params...)
}

// ExtractIDFromResponse extracts an ID from a successful response
func ExtractIDFromResponse(t *testing.T, resp map[string]interface{}) string {
	data, ok := resp["data"].(map[string]interface{})
	require.True(t, ok, "Expected data field in response")

	id, ok := data["id"].(string)
	require.True(t, ok, "Expected id field in data")
	require.NotEmpty(t, id, "Expected non-empty ID")

	return id
}
