package testutil

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/persistence/postgres"
)

// TestSeeds provides factory methods for creating test data
type TestSeeds struct {
	db *gorm.DB
}

// NewTestSeeds creates a new TestSeeds instance
func NewTestSeeds(db *gorm.DB) *TestSeeds {
	return &TestSeeds{db: db}
}

// CreateTestUser creates a test user
func (s *TestSeeds) CreateTestUser(username, email string) (*domain.User, error) {
	if username == "" {
		username = "testuser_" + uuid.New().String()[:8]
	}
	if email == "" {
		email = username + "@test.com"
	}

	user := domain.NewUser(username, email, "hashedpassword123")
	firstName := "Test"
	lastName := "User"
	user.FirstName = &firstName
	user.LastName = &lastName

	if err := s.db.Create(user).Error; err != nil {
		return nil, err
	}
	return user, nil
}

// CreateTestCategory creates a test category
func (s *TestSeeds) CreateTestCategory(name string, parentID *uuid.UUID) (*domain.Category, error) {
	if name == "" {
		name = "Test Category " + uuid.New().String()[:8]
	}

	description := "Test category description"
	category := domain.NewCategory(name, &description, parentID)

	// Convert to GORM model for database insertion
	var categoryModel postgres.CategoryModel
	categoryModel.FromDomain(category)

	if err := s.db.Create(&categoryModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return categoryModel.ToDomain(), nil
}

// CreateTestUnitOfMeasure creates a test unit of measure
func (s *TestSeeds) CreateTestUnitOfMeasure(name, symbol string, unitType domain.UnitOfMeasureType) (*domain.UnitOfMeasure, error) {
	if name == "" {
		name = "Test Unit " + uuid.New().String()[:8]
	}
	if symbol == "" {
		symbol = "tu" + uuid.New().String()[:4]
	}

	description := "Test unit description"
	unit := domain.NewUnitOfMeasure(name, symbol, unitType, &description)

	// Convert to GORM model for database insertion
	var unitModel postgres.UnitOfMeasureModel
	unitModel.FromDomain(unit)

	if err := s.db.Create(&unitModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return unitModel.ToDomain(), nil
}

// CreateTestProduct creates a test product
func (s *TestSeeds) CreateTestProduct(name string, categoryID uuid.UUID) (*domain.Product, error) {
	if name == "" {
		name = "Test Product " + uuid.New().String()[:8]
	}

	description := "Test product description"
	brand := "Test Brand"
	product := domain.NewProduct(name, &description, categoryID, &brand)

	// Convert to GORM model for database insertion
	var productModel postgres.ProductModel
	productModel.FromDomain(product)

	if err := s.db.Create(&productModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return productModel.ToDomain(), nil
}

// CreateTestProductVariant creates a test product variant
func (s *TestSeeds) CreateTestProductVariant(name string, productID, unitID uuid.UUID) (*domain.ProductVariant, error) {
	if name == "" {
		name = "Test Variant " + uuid.New().String()[:8]
	}

	description := "Test variant description"
	imageURL := "https://example.com/image.jpg"
	barcode := "123456789" + uuid.New().String()[:4]

	variant := domain.NewProductVariant(
		productID,
		name,
		&description,
		&barcode,
		&imageURL,
		domain.PackagingTypeSingle,
		&unitID,
		nil, // shelfLife - not used in current schema
	)

	// Convert to GORM model for database insertion
	var variantModel postgres.ProductVariantModel
	variantModel.FromDomain(variant)

	if err := s.db.Create(&variantModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return variantModel.ToDomain(), nil
}

// CreateTestPantry creates a test pantry
func (s *TestSeeds) CreateTestPantry(name string, ownerUserID uuid.UUID) (*domain.Pantry, error) {
	if name == "" {
		name = "Test Pantry " + uuid.New().String()[:8]
	}

	description := "Test pantry description"
	pantry := domain.NewPantry(name, &description, ownerUserID)

	// Convert to GORM model for database insertion
	var pantryModel postgres.PantryModel
	pantryModel.FromDomain(pantry)

	if err := s.db.Create(&pantryModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return pantryModel.ToDomain(), nil
}

// CreateTestPantryLocation creates a test pantry location
func (s *TestSeeds) CreateTestPantryLocation(name string, pantryID uuid.UUID) (*domain.PantryLocation, error) {
	if name == "" {
		name = "Test Location " + uuid.New().String()[:8]
	}

	description := "Test location description"
	location := domain.NewPantryLocation(pantryID, name, &description)

	// Convert to GORM model for database insertion
	var locationModel postgres.PantryLocationModel
	locationModel.FromDomain(location)

	if err := s.db.Create(&locationModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return locationModel.ToDomain(), nil
}

// CreateTestInventoryItem creates a test inventory item
func (s *TestSeeds) CreateTestInventoryItem(pantryID, variantID, unitID uuid.UUID, quantity float64) (*domain.InventoryItem, error) {
	now := time.Now()
	expirationDate := now.Add(30 * 24 * time.Hour) // 30 days from now
	purchasePrice := 10.50
	notes := "Test inventory item"

	item := domain.NewInventoryItem(
		pantryID,
		nil, // locationID
		variantID,
		quantity,
		unitID,
		&now,            // purchaseDate
		&expirationDate, // expirationDate
		&purchasePrice,
		&notes,
	)

	// Convert to GORM model for database insertion
	var itemModel postgres.InventoryItemModel
	itemModel.FromDomain(item)

	if err := s.db.Create(&itemModel).Error; err != nil {
		return nil, err
	}

	// Return the domain model with the generated ID
	return itemModel.ToDomain(), nil
}

// CreateTestRecipe creates a test recipe
func (s *TestSeeds) CreateTestRecipe(name string, userID uuid.UUID) (*domain.Recipe, error) {
	if name == "" {
		name = "Test Recipe " + uuid.New().String()[:8]
	}

	description := "Test recipe description"
	servings := 4
	prepTime := 15
	cookTime := 30
	difficulty := domain.DifficultyMedium
	isPublic := false

	unit := "cup"
	req := &domain.CreateRecipeRequest{
		Title:       name,
		Description: &description,
		Servings:    servings,
		PrepTime:    &prepTime,
		CookTime:    &cookTime,
		Difficulty:  difficulty,
		IsPublic:    isPublic,
		Ingredients: []domain.CreateRecipeIngredientRequest{
			{
				Name:     "Test Ingredient",
				Quantity: 1.0,
				Unit:     &unit,
			},
		},
		Instructions: []domain.CreateRecipeInstructionRequest{
			{
				Instruction: "Test instruction for the recipe",
			},
		},
	}

	recipe := domain.NewRecipe(userID, req)

	if err := s.db.Create(recipe).Error; err != nil {
		return nil, err
	}
	return recipe, nil
}

// CreateCompleteTestData creates a complete set of related test data
func (s *TestSeeds) CreateCompleteTestData() (*TestDataSet, error) {
	// Create user
	user, err := s.CreateTestUser("", "")
	if err != nil {
		return nil, err
	}

	// Create category
	category, err := s.CreateTestCategory("", nil)
	if err != nil {
		return nil, err
	}

	// Create unit of measure
	unit, err := s.CreateTestUnitOfMeasure("Kilogram", "kg", domain.UnitTypeWeight)
	if err != nil {
		return nil, err
	}

	// Create product
	product, err := s.CreateTestProduct("", category.ID)
	if err != nil {
		return nil, err
	}

	// Create product variant
	variant, err := s.CreateTestProductVariant("", product.ID, unit.ID)
	if err != nil {
		return nil, err
	}

	// Create pantry
	pantry, err := s.CreateTestPantry("", user.ID)
	if err != nil {
		return nil, err
	}

	// Create pantry location
	location, err := s.CreateTestPantryLocation("", pantry.ID)
	if err != nil {
		return nil, err
	}

	// Create inventory item
	inventory, err := s.CreateTestInventoryItem(pantry.ID, variant.ID, unit.ID, 5.0)
	if err != nil {
		return nil, err
	}

	// Create recipe
	recipe, err := s.CreateTestRecipe("", user.ID)
	if err != nil {
		return nil, err
	}

	return &TestDataSet{
		User:      user,
		Category:  category,
		Unit:      unit,
		Product:   product,
		Variant:   variant,
		Pantry:    pantry,
		Location:  location,
		Inventory: inventory,
		Recipe:    recipe,
	}, nil
}

// TestDataSet contains a complete set of related test data
type TestDataSet struct {
	User      *domain.User
	Category  *domain.Category
	Unit      *domain.UnitOfMeasure
	Product   *domain.Product
	Variant   *domain.ProductVariant
	Pantry    *domain.Pantry
	Location  *domain.PantryLocation
	Inventory *domain.InventoryItem
	Recipe    *domain.Recipe
}
