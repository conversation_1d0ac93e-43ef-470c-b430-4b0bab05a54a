package testutil

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestDatabase represents a test database instance
type TestDatabase struct {
	Container testcontainers.Container
	DB        *gorm.DB
	DSN       string
}

// SetupTestDatabase creates a PostgreSQL test container and returns a configured database
func SetupTestDatabase(ctx context.Context) (*TestDatabase, error) {
	// Create PostgreSQL container
	req := testcontainers.ContainerRequest{
		Image:        "postgres:15-alpine",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_DB":       "pantrypal_test",
			"POSTGRES_USER":     "test",
			"POSTGRES_PASSWORD": "test",
		},
		WaitingFor: wait.ForListeningPort("5432/tcp").WithStartupTimeout(60 * time.Second),
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to start container: %w", err)
	}

	// Get container host and port
	host, err := container.Host(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get container host: %w", err)
	}

	port, err := container.MappedPort(ctx, "5432")
	if err != nil {
		return nil, fmt.Errorf("failed to get container port: %w", err)
	}

	// Create DSN
	dsn := fmt.Sprintf("host=%s port=%s user=test password=test dbname=pantrypal_test sslmode=disable TimeZone=UTC",
		host, port.Port())

	// Connect to database with retry
	var db *gorm.DB
	for i := 0; i < 30; i++ {
		db, err = gorm.Open(postgres.Open(dsn), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Silent),
		})
		if err == nil {
			break
		}
		time.Sleep(time.Second)
	}

	if err != nil {
		container.Terminate(ctx)
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Run migrations
	if err := runMigrations(db); err != nil {
		container.Terminate(ctx)
		return nil, fmt.Errorf("failed to run migrations: %w", err)
	}

	return &TestDatabase{
		Container: container,
		DB:        db,
		DSN:       dsn,
	}, nil
}

// Cleanup terminates the test database container
func (td *TestDatabase) Cleanup(ctx context.Context) error {
	if td.Container != nil {
		return td.Container.Terminate(ctx)
	}
	return nil
}

// CleanupData removes all data from tables while preserving schema
func (td *TestDatabase) CleanupData() error {

	// Disable foreign key checks temporarily
	if err := td.DB.Exec("SET session_replication_role = replica").Error; err != nil {
		return fmt.Errorf("failed to disable foreign key checks: %w", err)
	}

	// Get all table names
	var tables []string
	if err := td.DB.Raw(`
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		AND tablename NOT LIKE 'schema_%'
	`).Scan(&tables).Error; err != nil {
		return fmt.Errorf("failed to get table names: %w", err)
	}

	// Truncate all tables
	for _, table := range tables {
		if err := td.DB.Exec(fmt.Sprintf("TRUNCATE TABLE %s CASCADE", table)).Error; err != nil {
			log.Printf("Warning: failed to truncate table %s: %v", table, err)
		}
	}

	// Re-enable foreign key checks
	if err := td.DB.Exec("SET session_replication_role = DEFAULT").Error; err != nil {
		return fmt.Errorf("failed to re-enable foreign key checks: %w", err)
	}

	return nil
}

// runMigrations runs the database migrations using SQL files
func runMigrations(db *gorm.DB) error {
	// Get the underlying SQL DB
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get SQL DB: %w", err)
	}

	// Enable UUID extension
	if err := db.Exec("CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\"").Error; err != nil {
		return fmt.Errorf("failed to enable uuid-ossp extension: %w", err)
	}

	// Run basic table creation for testing
	migrations := []string{
		// Users table
		`CREATE TABLE IF NOT EXISTS users (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			username VARCHAR(50) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			password_hash VARCHAR(255) NOT NULL,
			first_name VARCHAR(100),
			last_name VARCHAR(100),
			profile_picture_url VARCHAR(255),
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Refresh tokens table
		`CREATE TABLE IF NOT EXISTS refresh_tokens (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			token_hash VARCHAR(255) NOT NULL UNIQUE,
			expires_at TIMESTAMPTZ NOT NULL,
			is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
		)`,

		// Categories table
		`CREATE TABLE IF NOT EXISTS categories (
			category_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			name VARCHAR(100) NOT NULL,
			description TEXT,
			parent_category_id UUID REFERENCES categories(category_id) ON DELETE SET NULL,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Units of measure table
		`CREATE TABLE IF NOT EXISTS units_of_measure (
			unit_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			name VARCHAR(100) NOT NULL,
			symbol VARCHAR(20) NOT NULL,
			type VARCHAR(20) NOT NULL,
			description TEXT,
			is_base_unit BOOLEAN NOT NULL DEFAULT true,
			base_unit_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
			conversion_factor DECIMAL(20,10),
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Products table
		`CREATE TABLE IF NOT EXISTS products (
			product_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			name VARCHAR(255) NOT NULL,
			description TEXT,
			category_id UUID NOT NULL REFERENCES categories(category_id) ON DELETE RESTRICT,
			brand VARCHAR(100),
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Product variants table
		`CREATE TABLE IF NOT EXISTS product_variants (
			variant_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			product_id UUID NOT NULL REFERENCES products(product_id) ON DELETE CASCADE,
			name VARCHAR(255) NOT NULL,
			description TEXT,
			barcode_gtin VARCHAR(14),
			image_url VARCHAR(500),
			packaging_type VARCHAR(20) NOT NULL DEFAULT 'single',
			default_unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Pantries table
		`CREATE TABLE IF NOT EXISTS pantries (
			pantry_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			name VARCHAR(100) NOT NULL,
			description TEXT,
			owner_user_id UUID NOT NULL REFERENCES users(id) ON DELETE RESTRICT,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Pantry locations table
		`CREATE TABLE IF NOT EXISTS pantry_locations (
			pantry_location_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			name VARCHAR(100) NOT NULL,
			description TEXT,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Pantry memberships table
		`CREATE TABLE IF NOT EXISTS pantry_memberships (
			pantry_membership_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			role VARCHAR(20) NOT NULL DEFAULT 'member',
			status VARCHAR(20) NOT NULL DEFAULT 'active',
			joined_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			invited_by_user_id UUID REFERENCES users(id) ON DELETE SET NULL,
			deleted_at TIMESTAMPTZ
		)`,

		// Inventory items table
		`CREATE TABLE IF NOT EXISTS inventory_items (
			item_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			location_id UUID REFERENCES pantry_locations(pantry_location_id) ON DELETE SET NULL,
			product_variant_id UUID NOT NULL REFERENCES product_variants(variant_id) ON DELETE CASCADE,
			quantity DECIMAL(20,6) NOT NULL,
			unit_of_measure_id UUID NOT NULL REFERENCES units_of_measure(unit_id) ON DELETE RESTRICT,
			purchase_date TIMESTAMPTZ,
			expiration_date TIMESTAMPTZ,
			purchase_price DECIMAL(10,2),
			notes TEXT,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			deleted_at TIMESTAMPTZ
		)`,

		// Recipes table
		`CREATE TABLE IF NOT EXISTS recipes (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			title VARCHAR(200) NOT NULL,
			description TEXT,
			cuisine VARCHAR(100),
			category VARCHAR(100),
			difficulty VARCHAR(20) DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard', 'expert')),
			prep_time INTEGER,
			cook_time INTEGER,
			total_time INTEGER,
			servings INTEGER DEFAULT 4,
			calories INTEGER,
			is_public BOOLEAN DEFAULT FALSE,
			is_favorite BOOLEAN DEFAULT FALSE,
			rating DECIMAL(3,2),
			cook_count INTEGER DEFAULT 0,
			last_cooked_at TIMESTAMPTZ,
			source VARCHAR(500),
			notes TEXT,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Shopping lists table
		`CREATE TABLE IF NOT EXISTS shopping_lists (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			pantry_id UUID NOT NULL REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			name VARCHAR(255) NOT NULL,
			description TEXT,
			created_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'archived')),
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
		)`,

		// Shopping list items table
		`CREATE TABLE IF NOT EXISTS shopping_list_items (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			shopping_list_id UUID NOT NULL REFERENCES shopping_lists(id) ON DELETE CASCADE,
			product_variant_id UUID REFERENCES product_variants(variant_id) ON DELETE SET NULL,
			free_text_name VARCHAR(255),
			quantity_desired DECIMAL(10,3) NOT NULL CHECK (quantity_desired > 0),
			unit_of_measure_id UUID REFERENCES units_of_measure(unit_id) ON DELETE SET NULL,
			notes TEXT,
			is_purchased BOOLEAN NOT NULL DEFAULT FALSE,
			purchased_at TIMESTAMPTZ,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			CONSTRAINT check_item_reference CHECK (
				(product_variant_id IS NOT NULL AND free_text_name IS NULL) OR
				(product_variant_id IS NULL AND free_text_name IS NOT NULL)
			)
		)`,

		// Alert configurations table
		`CREATE TABLE IF NOT EXISTS alert_configurations (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			enabled BOOLEAN NOT NULL DEFAULT false,
			warning_days INTEGER NOT NULL DEFAULT 7,
			alert_days INTEGER NOT NULL DEFAULT 3,
			critical_days INTEGER NOT NULL DEFAULT 1,
			channels JSONB NOT NULL DEFAULT '[]',
			quiet_hours JSONB,
			category_filters JSONB NOT NULL DEFAULT '[]',
			min_value DECIMAL(10,2),
			last_checked TIMESTAMPTZ,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(user_id, pantry_id)
		)`,

		// Notifications table
		`CREATE TABLE IF NOT EXISTS notifications (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE CASCADE,
			type VARCHAR(50) NOT NULL,
			channel VARCHAR(20) NOT NULL,
			priority VARCHAR(10) NOT NULL,
			title VARCHAR(255) NOT NULL,
			message TEXT NOT NULL,
			data JSONB,
			status VARCHAR(20) NOT NULL DEFAULT 'pending',
			scheduled_at TIMESTAMPTZ,
			sent_at TIMESTAMPTZ,
			error TEXT,
			created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe tags table
		`CREATE TABLE IF NOT EXISTS recipe_tags (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			name VARCHAR(100) NOT NULL UNIQUE,
			description TEXT,
			color VARCHAR(7),
			icon VARCHAR(50),
			is_system BOOLEAN DEFAULT FALSE,
			usage_count INTEGER DEFAULT 0,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipes table
		`CREATE TABLE IF NOT EXISTS recipes (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			title VARCHAR(200) NOT NULL,
			description TEXT,
			cuisine VARCHAR(100),
			category VARCHAR(100),
			difficulty VARCHAR(20) DEFAULT 'medium' CHECK (difficulty IN ('easy', 'medium', 'hard', 'expert')),
			prep_time INTEGER,
			cook_time INTEGER,
			total_time INTEGER,
			servings INTEGER DEFAULT 4,
			calories INTEGER,
			is_public BOOLEAN DEFAULT FALSE,
			is_favorite BOOLEAN DEFAULT FALSE,
			rating DECIMAL(3,2),
			cook_count INTEGER DEFAULT 0,
			last_cooked_at TIMESTAMPTZ,
			source VARCHAR(500),
			notes TEXT,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe ingredients table
		`CREATE TABLE IF NOT EXISTS recipe_ingredients (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			product_variant_id UUID REFERENCES product_variants(variant_id),
			name VARCHAR(200) NOT NULL,
			quantity DECIMAL(10,3) NOT NULL,
			unit_of_measure_id UUID REFERENCES units_of_measure(unit_id),
			unit VARCHAR(50),
			preparation VARCHAR(200),
			is_optional BOOLEAN DEFAULT FALSE,
			is_garnish BOOLEAN DEFAULT FALSE,
			"order" INTEGER NOT NULL,
			notes VARCHAR(500),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe instructions table
		`CREATE TABLE IF NOT EXISTS recipe_instructions (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			step_number INTEGER NOT NULL,
			title VARCHAR(200),
			instruction TEXT NOT NULL,
			duration INTEGER,
			temperature INTEGER,
			image_url VARCHAR(500),
			video_url VARCHAR(500),
			tips TEXT,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe media table
		`CREATE TABLE IF NOT EXISTS recipe_media (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			type VARCHAR(20) NOT NULL CHECK (type IN ('image', 'video')),
			url VARCHAR(500) NOT NULL,
			filename VARCHAR(255) NOT NULL,
			size BIGINT,
			mime_type VARCHAR(100),
			width INTEGER,
			height INTEGER,
			duration INTEGER,
			is_main BOOLEAN DEFAULT FALSE,
			"order" INTEGER DEFAULT 0,
			caption VARCHAR(500),
			alt_text VARCHAR(200),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe nutrition table
		`CREATE TABLE IF NOT EXISTS recipe_nutrition (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			recipe_id UUID NOT NULL UNIQUE REFERENCES recipes(id) ON DELETE CASCADE,
			calories INTEGER,
			protein DECIMAL(8,2),
			carbohydrates DECIMAL(8,2),
			fat DECIMAL(8,2),
			fiber DECIMAL(8,2),
			sugar DECIMAL(8,2),
			sodium DECIMAL(8,2),
			cholesterol DECIMAL(8,2),
			vitamin_a DECIMAL(8,2),
			vitamin_c DECIMAL(8,2),
			calcium DECIMAL(8,2),
			iron DECIMAL(8,2),
			serving_size VARCHAR(100),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe reviews table
		`CREATE TABLE IF NOT EXISTS recipe_reviews (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
			comment TEXT,
			image_url VARCHAR(500),
			cooked_at TIMESTAMPTZ,
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(recipe_id, user_id)
		)`,

		// Recipe collections table
		`CREATE TABLE IF NOT EXISTS recipe_collections (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
			name VARCHAR(200) NOT NULL,
			description TEXT,
			is_public BOOLEAN DEFAULT FALSE,
			image_url VARCHAR(500),
			created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
		)`,

		// Recipe collection items table
		`CREATE TABLE IF NOT EXISTS recipe_collection_items (
			id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
			collection_id UUID NOT NULL REFERENCES recipe_collections(id) ON DELETE CASCADE,
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			"order" INTEGER DEFAULT 0,
			notes VARCHAR(500),
			added_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(collection_id, recipe_id)
		)`,

		// Recipe-tag relationship table
		`CREATE TABLE IF NOT EXISTS recipe_recipe_tags (
			recipe_id UUID NOT NULL REFERENCES recipes(id) ON DELETE CASCADE,
			recipe_tag_id UUID NOT NULL REFERENCES recipe_tags(id) ON DELETE CASCADE,
			PRIMARY KEY (recipe_id, recipe_tag_id)
		)`,
	}

	// Execute each migration
	for _, migration := range migrations {
		if err := db.Exec(migration).Error; err != nil {
			return fmt.Errorf("failed to execute migration: %w", err)
		}
	}

	_ = sqlDB // Use sqlDB to avoid unused variable error
	return nil
}

// GetSQLDB returns the underlying sql.DB instance
func (td *TestDatabase) GetSQLDB() (*sql.DB, error) {
	return td.DB.DB()
}

// Transaction executes a function within a database transaction
func (td *TestDatabase) Transaction(fn func(*gorm.DB) error) error {
	return td.DB.Transaction(fn)
}
