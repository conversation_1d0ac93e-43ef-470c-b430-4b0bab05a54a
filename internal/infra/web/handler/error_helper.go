package handler

import (
	"fmt"

	"github.com/gofiber/fiber/v2"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// ErrorLogContext represents context information for error logging
type ErrorLogContext struct {
	Operation   string                 `json:"operation"`
	UserID      string                 `json:"user_id,omitempty"`
	Resource    string                 `json:"resource,omitempty"`
	ResourceID  string                 `json:"resource_id,omitempty"`
	RequestData interface{}            `json:"request_data,omitempty"`
	Additional  map[string]interface{} `json:"additional,omitempty"`
}

// LogAndReturnError logs an error with appropriate categorization and returns fiber error response
func LogAndReturnError(c *fiber.Ctx, logger *logger.Logger, err error, context ErrorLogContext) error {
	// Build logging context
	logContext := buildLogContext(context)

	// Log based on error type
	if appErr := errors.GetAppError(err); appErr != nil {
		logAppError(logger, appErr, context, logContext)
	} else {
		logger.LogSystemError(err, "handler", logContext)
	}

	return ErrorResponse(c, err)
}

// buildLogContext creates a comprehensive logging context
func buildLogContext(context ErrorLogContext) map[string]interface{} {
	logContext := map[string]interface{}{
		"operation": context.Operation,
	}

	if context.UserID != "" {
		logContext["user_id"] = context.UserID
	}

	if context.Resource != "" {
		logContext["resource"] = context.Resource
	}

	if context.ResourceID != "" {
		logContext["resource_id"] = context.ResourceID
	}

	if context.RequestData != nil {
		logContext["request_data"] = context.RequestData
	}

	// Add additional context
	if context.Additional != nil {
		for key, value := range context.Additional {
			logContext[key] = value
		}
	}

	return logContext
}

// logAppError logs application errors with appropriate categorization
func logAppError(logger *logger.Logger, appErr *errors.AppError, context ErrorLogContext, logContext map[string]interface{}) {
	switch appErr.Code {
	case errors.ErrCodeValidationFailed, errors.ErrCodeInvalidInput, errors.ErrCodeMissingRequiredField:
		// Extract validation fields if available
		var validationFields map[string]string
		if appErr.Details != nil {
			if fields, ok := appErr.Details["fields"].(map[string]string); ok {
				validationFields = fields
			}
		}
		logger.LogValidationError(appErr, validationFields, logContext)

	case errors.ErrCodeDatabaseError:
		operation := context.Operation
		if context.Resource != "" {
			operation = context.Resource + "_" + operation
		}
		logger.LogDatabaseError(appErr, operation, logContext)

	case errors.ErrCodeExternalServiceError:
		serviceName := "unknown"
		if appErr.Details != nil {
			if service, ok := appErr.Details["service"].(string); ok {
				serviceName = service
			}
		}
		logger.LogExternalServiceError(appErr, serviceName, logContext)

	case errors.ErrCodeInvalidCredentials, errors.ErrCodeTokenExpired, errors.ErrCodeTokenInvalid, errors.ErrCodeUnauthorized:
		logger.LogAuthenticationError(appErr, logContext)

	case errors.ErrCodeForbidden, errors.ErrCodeInsufficientPermissions:
		resource := context.Resource
		if resource == "" {
			resource = "unknown"
		}
		logger.LogAuthorizationError(appErr, resource, logContext)

	case errors.ErrCodeBusinessRuleViolation:
		rule := "unknown"
		if appErr.Details != nil {
			if r, ok := appErr.Details["rule"].(string); ok {
				rule = r
			}
		}
		logger.LogBusinessError(appErr, rule, logContext)

	case errors.ErrCodeRateLimitExceeded:
		// Extract rate limit details if available
		limit := 0
		if appErr.Details != nil {
			if l, ok := appErr.Details["limit"].(int); ok {
				limit = l
			}
		}
		logger.LogRateLimitError(appErr, limit, 0, logContext)

	default:
		logger.LogErrorWithCategory(appErr, string(appErr.Code), getErrorMessage(appErr.Code, context), logContext)
	}
}

// getErrorMessage returns a descriptive error message based on error code and context
func getErrorMessage(code errors.ErrorCode, context ErrorLogContext) string {
	operation := context.Operation
	if operation == "" {
		operation = "operation"
	}

	switch code {
	case errors.ErrCodeNotFound:
		if context.Resource != "" {
			return fmt.Sprintf("%s not found during %s", context.Resource, operation)
		}
		return fmt.Sprintf("Resource not found during %s", operation)

	case errors.ErrCodeAlreadyExists:
		if context.Resource != "" {
			return fmt.Sprintf("%s already exists during %s", context.Resource, operation)
		}
		return fmt.Sprintf("Resource already exists during %s", operation)

	case errors.ErrCodeConflict:
		return fmt.Sprintf("Conflict occurred during %s", operation)

	case errors.ErrCodeInvalidOperation:
		return fmt.Sprintf("Invalid operation: %s", operation)

	case errors.ErrCodeQuotaExceeded:
		return fmt.Sprintf("Quota exceeded during %s", operation)

	case errors.ErrCodeServiceUnavailable:
		return fmt.Sprintf("Service unavailable during %s", operation)

	case errors.ErrCodeInternalError:
		return fmt.Sprintf("Internal error during %s", operation)

	default:
		return fmt.Sprintf("Error occurred during %s", operation)
	}
}

// Helper functions for common error logging patterns

// LogValidationError logs validation errors with field details
func LogValidationError(c *fiber.Ctx, logger *logger.Logger, err error, context ErrorLogContext) error {
	context.Operation = "validation"
	return LogAndReturnError(c, logger, err, context)
}

// LogNotFoundError logs not found errors
func LogNotFoundError(c *fiber.Ctx, logger *logger.Logger, err error, resource, resourceID string, context ErrorLogContext) error {
	context.Resource = resource
	context.ResourceID = resourceID
	if context.Operation == "" {
		context.Operation = "get_" + resource
	}
	return LogAndReturnError(c, logger, err, context)
}

// LogAuthorizationError logs authorization errors
func LogAuthorizationError(c *fiber.Ctx, logger *logger.Logger, err error, resource string, context ErrorLogContext) error {
	context.Resource = resource
	if context.Operation == "" {
		context.Operation = "access_" + resource
	}
	return LogAndReturnError(c, logger, err, context)
}

// LogDatabaseError logs database errors
func LogDatabaseError(c *fiber.Ctx, logger *logger.Logger, err error, operation string, context ErrorLogContext) error {
	context.Operation = operation
	if context.Additional == nil {
		context.Additional = make(map[string]interface{})
	}
	context.Additional["error_category"] = "database"
	return LogAndReturnError(c, logger, err, context)
}

// LogBusinessLogicError logs business logic errors
func LogBusinessLogicError(c *fiber.Ctx, logger *logger.Logger, err error, rule string, context ErrorLogContext) error {
	if context.Additional == nil {
		context.Additional = make(map[string]interface{})
	}
	context.Additional["business_rule"] = rule
	context.Additional["error_category"] = "business_logic"
	return LogAndReturnError(c, logger, err, context)
}
