package handler

import (
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v2"
	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userRepo    domain.UserRepository
	authService domain.AuthService
	logger      *logger.Logger
	validator   *validator.Validate
}

// NewUserHandler creates a new user handler
func NewUserHandler(userRepo domain.UserRepository, authService domain.AuthService, log *logger.Logger) *UserHandler {
	return &UserHandler{
		userRepo:    userRepo,
		authService: authService,
		logger:      log,
		validator:   validator.New(),
	}
}

// GetProfile retrieves the current user's profile
//
//	@Summary		Get user profile
//	@Description	Retrieve the current authenticated user's profile information
//	@Tags			Users
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Success		200	{object}	APIResponse	"Profile retrieved successfully"
//	@Failure		401	{object}	APIResponse	"Unauthorized"
//	@Failure		404	{object}	APIResponse	"User not found"
//	@Failure		500	{object}	APIResponse	"Internal server error"
//	@Router			/users/profile [get]
func (h *UserHandler) GetProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	// Get user from database
	user, err := h.userRepo.GetByID(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get user profile", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Return user profile
	response := map[string]interface{}{
		"id":                  user.ID,
		"username":            user.Username,
		"email":               user.Email,
		"first_name":          user.FirstName,
		"last_name":           user.LastName,
		"profile_picture_url": user.ProfilePictureURL,
		"created_at":          user.CreatedAt,
		"updated_at":          user.UpdatedAt,
	}

	return SuccessResponse(c, response, "Profile retrieved successfully")
}

// UpdateProfile updates the current user's profile
//
//	@Summary		Update user profile
//	@Description	Update the current authenticated user's profile information
//	@Tags			Users
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			request	body		domain.UpdateProfileRequest	true	"Profile update data"
//	@Success		200		{object}	APIResponse					"Profile updated successfully"
//	@Failure		400		{object}	APIResponse					"Invalid input or validation error"
//	@Failure		401		{object}	APIResponse					"Unauthorized"
//	@Failure		404		{object}	APIResponse					"User not found"
//	@Failure		500		{object}	APIResponse					"Internal server error"
//	@Router			/users/profile [put]
func (h *UserHandler) UpdateProfile(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.UpdateProfileRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get current user
	user, err := h.userRepo.GetByID(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get user for profile update", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Update profile
	user.UpdateProfile(req.FirstName, req.LastName, req.ProfilePictureURL)

	// Save changes
	if err := h.userRepo.Update(user); err != nil {
		h.logger.LogError(err, "Failed to update user profile", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Log profile update
	h.logger.LogBusinessEvent("user.profile_updated", userIDStr, map[string]interface{}{
		"email": user.Email,
	})

	// Return updated profile
	response := map[string]interface{}{
		"id":                  user.ID,
		"username":            user.Username,
		"email":               user.Email,
		"first_name":          user.FirstName,
		"last_name":           user.LastName,
		"profile_picture_url": user.ProfilePictureURL,
		"updated_at":          user.UpdatedAt,
	}

	return SuccessResponse(c, response, "Profile updated successfully")
}

// ChangePassword changes the current user's password
//
//	@Summary		Change user password
//	@Description	Change the current authenticated user's password
//	@Tags			Users
//	@Accept			json
//	@Produce		json
//	@Security		BearerAuth
//	@Param			request	body		domain.ChangePasswordRequest	true	"Password change data"
//	@Success		200		{object}	APIResponse						"Password changed successfully"
//	@Failure		400		{object}	APIResponse						"Invalid input or validation error"
//	@Failure		401		{object}	APIResponse						"Unauthorized or invalid current password"
//	@Failure		500		{object}	APIResponse						"Internal server error"
//	@Router			/users/change-password [post]
func (h *UserHandler) ChangePassword(c *fiber.Ctx) error {
	// Get user ID from context
	userIDStr, ok := getUserIDFromContext(c)
	if !ok {
		return ErrorResponse(c, errors.ErrUnauthorized)
	}

	_, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	var req domain.ChangePasswordRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid request body"))
	}

	// Validate request
	if err := h.validator.Struct(&req); err != nil {
		validationErrors := h.parseValidationErrors(err)
		return ValidationErrorResponse(c, errors.NewValidationError("Validation failed", validationErrors))
	}

	// Get current user
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidInput, "Invalid user ID"))
	}

	user, err := h.userRepo.GetByID(userID)
	if err != nil {
		h.logger.LogError(err, "Failed to get user for password change", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Verify current password
	if err := h.authService.VerifyPassword(req.CurrentPassword, user.PasswordHash); err != nil {
		return ErrorResponse(c, errors.New(errors.ErrCodeInvalidCredentials, "Current password is incorrect"))
	}

	// Hash new password
	newPasswordHash, err := h.authService.HashPassword(req.NewPassword)
	if err != nil {
		h.logger.LogError(err, "Failed to hash new password", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, errors.ErrInternalError)
	}

	// Update password
	user.ChangePassword(newPasswordHash)

	// Save changes
	if err := h.userRepo.Update(user); err != nil {
		h.logger.LogError(err, "Failed to update user password", map[string]interface{}{
			"user_id": userIDStr,
		})
		return ErrorResponse(c, err)
	}

	// Revoke all refresh tokens to force re-login on all devices
	if err := h.authService.RevokeAllUserTokens(userID); err != nil {
		h.logger.LogError(err, "Failed to revoke user tokens after password change", map[string]interface{}{
			"user_id": userIDStr,
		})
		// Don't fail the request if token revocation fails, just log it
	}

	// Log password change
	h.logger.LogBusinessEvent("user.password_changed", userIDStr, map[string]interface{}{
		"email": user.Email,
	})

	return SuccessResponse(c, nil, "Password changed successfully")
}

// Helper methods

func (h *UserHandler) parseValidationErrors(err error) map[string]string {
	validationErrors := make(map[string]string)

	if validationErr, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErr {
			field := strings.ToLower(fieldErr.Field())
			switch fieldErr.Tag() {
			case "required":
				validationErrors[field] = "This field is required"
			case "email":
				validationErrors[field] = "Invalid email format"
			case "min":
				validationErrors[field] = "Value is too short"
			case "max":
				validationErrors[field] = "Value is too long"
			case "url":
				validationErrors[field] = "Invalid URL format"
			case "eqfield":
				validationErrors[field] = "Values do not match"
			default:
				validationErrors[field] = "Invalid value"
			}
		}
	}

	return validationErrors
}

// getUserIDFromContext extracts user ID from fiber context
func getUserIDFromContext(c *fiber.Ctx) (string, bool) {
	userID := c.Locals("user_id")
	if userID == nil {
		return "", false
	}

	userIDStr, ok := userID.(string)
	return userIDStr, ok
}
