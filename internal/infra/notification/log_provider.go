package notification

import (
	"context"
	"encoding/json"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// LogProvider implements a notification provider that logs notifications
// This is useful for development and testing
type LogProvider struct {
	logger  *logger.Logger
	enabled bool
}

// NewLogProvider creates a new log notification provider
func NewLogProvider(logger *logger.Logger, enabled bool) domain.NotificationProvider {
	return &LogProvider{
		logger:  logger,
		enabled: enabled,
	}
}

// GetChannel returns the notification channel this provider handles
func (p *LogProvider) GetChannel() domain.NotificationChannel {
	return domain.NotificationChannelInApp
}

// Send sends a notification by logging it
func (p *LogProvider) Send(ctx context.Context, notification *domain.Notification) error {
	// Serialize notification data for logging
	var dataStr string
	if notification.Data != nil {
		if dataJSON, err := json.Marshal(notification.Data); err == nil {
			dataStr = string(dataJSON)
		}
	}

	// Log the notification
	event := p.logger.Info()
	event.Str("notification_id", notification.ID.String()).
		Str("user_id", notification.UserID.String()).
		Str("type", string(notification.Type)).
		Str("channel", string(notification.Channel)).
		Str("priority", string(notification.Priority)).
		Str("title", notification.Title).
		Str("message", notification.Message).
		Str("data", dataStr)

	if notification.PantryID != nil {
		event.Str("pantry_id", notification.PantryID.String())
	}

	if notification.ScheduledAt != nil {
		event.Time("scheduled_at", *notification.ScheduledAt)
	}

	event.Msg("Notification sent via log provider")

	return nil
}

// IsEnabled returns whether this provider is enabled
func (p *LogProvider) IsEnabled() bool {
	return p.enabled
}

// ValidateConfig validates the provider configuration
func (p *LogProvider) ValidateConfig() error {
	// Log provider doesn't need any special configuration
	return nil
}

// ConsoleProvider implements a notification provider that prints to console
// This is useful for development
type ConsoleProvider struct {
	enabled bool
}

// NewConsoleProvider creates a new console notification provider
func NewConsoleProvider(enabled bool) domain.NotificationProvider {
	return &ConsoleProvider{
		enabled: enabled,
	}
}

// GetChannel returns the notification channel this provider handles
func (p *ConsoleProvider) GetChannel() domain.NotificationChannel {
	return domain.NotificationChannelInApp
}

// Send sends a notification by printing to console
func (p *ConsoleProvider) Send(ctx context.Context, notification *domain.Notification) error {
	// Print notification to console
	println("=== NOTIFICATION ===")
	println("ID:", notification.ID.String())
	println("User ID:", notification.UserID.String())
	if notification.PantryID != nil {
		println("Pantry ID:", notification.PantryID.String())
	}
	println("Type:", string(notification.Type))
	println("Channel:", string(notification.Channel))
	println("Priority:", string(notification.Priority))
	println("Title:", notification.Title)
	println("Message:", notification.Message)

	if notification.Data != nil {
		if dataJSON, err := json.Marshal(notification.Data); err == nil {
			println("Data:", string(dataJSON))
		}
	}

	if notification.ScheduledAt != nil {
		println("Scheduled At:", notification.ScheduledAt.String())
	}
	println("==================")

	return nil
}

// IsEnabled returns whether this provider is enabled
func (p *ConsoleProvider) IsEnabled() bool {
	return p.enabled
}

// ValidateConfig validates the provider configuration
func (p *ConsoleProvider) ValidateConfig() error {
	// Console provider doesn't need any special configuration
	return nil
}
