package postgres

import (
	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// UserRepository implements domain.UserRepository using GORM
type UserRepository struct {
	db *gorm.DB
}

// NewUserRepository creates a new user repository
func NewUserRepository(db *gorm.DB) *UserRepository {
	return &UserRepository{db: db}
}

// Create creates a new user
func (r *UserRepository) Create(user *domain.User) error {
	model := &UserModel{}
	model.FromDomain(user)

	if err := r.db.Create(model).Error; err != nil {
		// Enhanced error logging with detailed context
		context := map[string]interface{}{
			"user_email":    user.Email,
			"user_username": user.Username,
			"operation":     "create",
			"table":         "users",
		}

		if isDuplicateKeyError(err) {
			if isDuplicateEmail(err) {
				// Log the duplicate email error with context
				logger.QuickLogRepositoryError(err, "user", "create_duplicate_email")
				return errors.New(errors.ErrCodeAlreadyExists, "email already exists")
			}
			if isDuplicateUsername(err) {
				// Log the duplicate username error with context
				logger.QuickLogRepositoryError(err, "user", "create_duplicate_username")
				return errors.New(errors.ErrCodeAlreadyExists, "username already exists")
			}
		}

		// Log general database error with enhanced context
		logger.LogRepositoryError(err, "user", "create", logger.ErrorContext{
			EntityID:   user.ID.String(),
			Additional: context,
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create user")
	}

	// Update the domain object with generated values
	*user = *model.ToDomain()

	return nil
}

// GetByID retrieves a user by ID
func (r *UserRepository) GetByID(id uuid.UUID) (*domain.User, error) {
	var model UserModel

	if err := r.db.Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Log not found with context (this might be expected, so use debug level)
			logger.LogRepositoryError(err, "user", "get_by_id", logger.ErrorContext{
				EntityID: id.String(),
				Additional: map[string]interface{}{
					"lookup_type": "id",
					"expected":    true, // This indicates it's an expected error
				},
			})
			return nil, errors.New(errors.ErrCodeNotFound, "user not found")
		}

		// Log unexpected database error
		logger.LogRepositoryError(err, "user", "get_by_id", logger.ErrorContext{
			EntityID: id.String(),
			Additional: map[string]interface{}{
				"lookup_type": "id",
				"query":       "SELECT * FROM users WHERE id = ?",
			},
		})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user by ID")
	}

	return model.ToDomain(), nil
}

// GetByEmail retrieves a user by email
func (r *UserRepository) GetByEmail(email string) (*domain.User, error) {
	var model UserModel

	if err := r.db.Where("email = ?", email).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "user not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user by email")
	}

	return model.ToDomain(), nil
}

// GetByUsername retrieves a user by username
func (r *UserRepository) GetByUsername(username string) (*domain.User, error) {
	var model UserModel

	if err := r.db.Where("username = ?", username).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "user not found")
		}
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get user by username")
	}

	return model.ToDomain(), nil
}

// Update updates an existing user
func (r *UserRepository) Update(user *domain.User) error {
	model := &UserModel{}
	model.FromDomain(user)

	result := r.db.Model(&UserModel{}).Where("id = ?", user.ID).Updates(model)
	if result.Error != nil {
		if isDuplicateKeyError(result.Error) {
			if isDuplicateEmail(result.Error) {
				return errors.New(errors.ErrCodeAlreadyExists, "email already exists")
			}
			if isDuplicateUsername(result.Error) {
				return errors.New(errors.ErrCodeAlreadyExists, "username already exists")
			}
		}
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to update user")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "user not found")
	}

	return nil
}

// Delete soft deletes a user
func (r *UserRepository) Delete(id uuid.UUID) error {
	result := r.db.Delete(&UserModel{}, id)
	if result.Error != nil {
		return errors.Wrap(result.Error, errors.ErrCodeDatabaseError, "failed to delete user")
	}

	if result.RowsAffected == 0 {
		return errors.New(errors.ErrCodeNotFound, "user not found")
	}

	return nil
}

// ExistsByEmail checks if a user exists with the given email
func (r *UserRepository) ExistsByEmail(email string) (bool, error) {
	var count int64

	if err := r.db.Model(&UserModel{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check email existence")
	}

	return count > 0, nil
}

// ExistsByUsername checks if a user exists with the given username
func (r *UserRepository) ExistsByUsername(username string) (bool, error) {
	var count int64

	if err := r.db.Model(&UserModel{}).Where("username = ?", username).Count(&count).Error; err != nil {
		return false, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to check username existence")
	}

	return count > 0, nil
}

// Helper functions to check for specific database errors

// isDuplicateKeyError checks if the error is a duplicate key constraint violation
func isDuplicateKeyError(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	// PostgreSQL duplicate key error patterns
	return contains(errStr, "duplicate key value violates unique constraint") ||
		contains(errStr, "UNIQUE constraint failed")
}

// isDuplicateEmail checks if the error is specifically for email duplication
func isDuplicateEmail(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return contains(errStr, "email") || contains(errStr, "users_email_key")
}

// isDuplicateUsername checks if the error is specifically for username duplication
func isDuplicateUsername(err error) bool {
	if err == nil {
		return false
	}

	errStr := err.Error()
	return contains(errStr, "username") || contains(errStr, "users_username_key")
}

// contains checks if a string contains a substring (case-insensitive)
func contains(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			len(s) > len(substr) &&
				(s[:len(substr)] == substr ||
					s[len(s)-len(substr):] == substr ||
					containsSubstring(s, substr)))
}

// containsSubstring performs a simple substring search
func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
