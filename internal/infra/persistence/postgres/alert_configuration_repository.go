package postgres

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/core/usecases"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// AlertConfigurationModel represents the database model for alert configurations
type AlertConfigurationModel struct {
	ID              uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID          uuid.UUID  `gorm:"type:uuid;not null;index"`
	PantryID        *uuid.UUID `gorm:"type:uuid;index"`
	Enabled         bool       `gorm:"not null;default:false"`
	WarningDays     int        `gorm:"not null;default:7"`
	AlertDays       int        `gorm:"not null;default:3"`
	CriticalDays    int        `gorm:"not null;default:1"`
	Channels        string     `gorm:"type:jsonb"` // JSON array of notification channels
	QuietHours      *string    `gorm:"type:jsonb"` // JSON object for quiet hours
	CategoryFilters string     `gorm:"type:jsonb"` // JSON array of category UUIDs
	MinValue        *float64   `gorm:"type:decimal(10,2)"`
	LastChecked     *time.Time
	CreatedAt       time.Time `gorm:"not null"`
	UpdatedAt       time.Time `gorm:"not null"`

	// Foreign key constraints
	User   UserModel    `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE"`
	Pantry *PantryModel `gorm:"foreignKey:PantryID;constraint:OnDelete:CASCADE"`
}

// TableName returns the table name for the model
func (AlertConfigurationModel) TableName() string {
	return "alert_configurations"
}

// ToDomain converts the model to domain entity
func (m *AlertConfigurationModel) ToDomain() *domain.AlertConfiguration {
	config := &domain.AlertConfiguration{
		ID:           m.ID,
		UserID:       m.UserID,
		PantryID:     m.PantryID,
		Enabled:      m.Enabled,
		WarningDays:  m.WarningDays,
		AlertDays:    m.AlertDays,
		CriticalDays: m.CriticalDays,
		MinValue:     m.MinValue,
		LastChecked:  m.LastChecked,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
	}

	// Parse channels
	if m.Channels != "" {
		var channels []domain.NotificationChannel
		if err := json.Unmarshal([]byte(m.Channels), &channels); err == nil {
			config.Channels = channels
		}
	}

	// Parse quiet hours
	if m.QuietHours != nil && *m.QuietHours != "" {
		var quietHours domain.QuietHours
		if err := json.Unmarshal([]byte(*m.QuietHours), &quietHours); err == nil {
			config.QuietHours = &quietHours
		}
	}

	// Parse category filters
	if m.CategoryFilters != "" {
		var categoryFilters []uuid.UUID
		if err := json.Unmarshal([]byte(m.CategoryFilters), &categoryFilters); err == nil {
			config.CategoryFilters = categoryFilters
		}
	}

	return config
}

// FromDomain converts domain entity to model
func (m *AlertConfigurationModel) FromDomain(config *domain.AlertConfiguration) {
	m.ID = config.ID
	m.UserID = config.UserID
	m.PantryID = config.PantryID
	m.Enabled = config.Enabled
	m.WarningDays = config.WarningDays
	m.AlertDays = config.AlertDays
	m.CriticalDays = config.CriticalDays
	m.MinValue = config.MinValue
	m.LastChecked = config.LastChecked
	m.CreatedAt = config.CreatedAt
	m.UpdatedAt = config.UpdatedAt

	// Serialize channels (always set to valid JSON, even if empty)
	if channelsJSON, err := json.Marshal(config.Channels); err == nil {
		m.Channels = string(channelsJSON)
	} else {
		m.Channels = "[]" // Default to empty array
	}

	// Serialize quiet hours
	if config.QuietHours != nil {
		if quietHoursJSON, err := json.Marshal(config.QuietHours); err == nil {
			quietHoursStr := string(quietHoursJSON)
			m.QuietHours = &quietHoursStr
		}
	}

	// Serialize category filters (always set to valid JSON, even if empty)
	if filtersJSON, err := json.Marshal(config.CategoryFilters); err == nil {
		m.CategoryFilters = string(filtersJSON)
	} else {
		m.CategoryFilters = "[]" // Default to empty array
	}
}

// AlertConfigurationRepository implements the alert configuration repository interface
type AlertConfigurationRepository struct {
	db *gorm.DB
}

// NewAlertConfigurationRepository creates a new alert configuration repository
func NewAlertConfigurationRepository(db *gorm.DB) usecases.AlertConfigurationRepository {
	return &AlertConfigurationRepository{
		db: db,
	}
}

// Create creates a new alert configuration
func (r *AlertConfigurationRepository) Create(config *domain.AlertConfiguration) error {
	model := &AlertConfigurationModel{}
	model.FromDomain(config)

	if err := r.db.Create(model).Error; err != nil {
		if isDuplicateKeyError(err) {
			return errors.New(errors.ErrCodeAlreadyExists, "alert configuration already exists")
		}

		logger.LogRepositoryError(err, "alert_configuration", "create", logger.ErrorContext{
			EntityID: config.ID.String(),
			Additional: map[string]interface{}{
				"user_id":   config.UserID.String(),
				"pantry_id": config.PantryID,
			},
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create alert configuration")
	}

	// Update the domain object with generated values
	*config = *model.ToDomain()
	return nil
}

// GetByID retrieves an alert configuration by ID
func (r *AlertConfigurationRepository) GetByID(id uuid.UUID) (*domain.AlertConfiguration, error) {
	var model AlertConfigurationModel

	if err := r.db.Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "alert configuration not found")
		}

		logger.LogRepositoryError(err, "alert_configuration", "get_by_id", logger.ErrorContext{
			EntityID: id.String(),
		})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get alert configuration")
	}

	return model.ToDomain(), nil
}

// GetByUserAndPantry retrieves alert configuration by user and pantry
func (r *AlertConfigurationRepository) GetByUserAndPantry(userID uuid.UUID, pantryID *uuid.UUID) (*domain.AlertConfiguration, error) {
	var model AlertConfigurationModel
	query := r.db.Where("user_id = ?", userID)

	if pantryID != nil {
		query = query.Where("pantry_id = ?", *pantryID)
	} else {
		query = query.Where("pantry_id IS NULL")
	}

	if err := query.First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "alert configuration not found")
		}

		logger.LogRepositoryError(err, "alert_configuration", "get_by_user_and_pantry", logger.ErrorContext{
			Additional: map[string]interface{}{
				"user_id":   userID.String(),
				"pantry_id": pantryID,
			},
		})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get alert configuration")
	}

	return model.ToDomain(), nil
}

// GetActiveConfigurations retrieves all active alert configurations
func (r *AlertConfigurationRepository) GetActiveConfigurations() ([]*domain.AlertConfiguration, error) {
	var models []AlertConfigurationModel

	if err := r.db.Where("enabled = ?", true).Find(&models).Error; err != nil {
		logger.LogRepositoryError(err, "alert_configuration", "get_active_configurations", logger.ErrorContext{})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get active alert configurations")
	}

	configs := make([]*domain.AlertConfiguration, len(models))
	for i, model := range models {
		configs[i] = model.ToDomain()
	}

	return configs, nil
}

// Update updates an alert configuration
func (r *AlertConfigurationRepository) Update(config *domain.AlertConfiguration) error {
	model := &AlertConfigurationModel{}
	model.FromDomain(config)

	if err := r.db.Save(model).Error; err != nil {
		logger.LogRepositoryError(err, "alert_configuration", "update", logger.ErrorContext{
			EntityID: config.ID.String(),
			Additional: map[string]interface{}{
				"user_id":   config.UserID.String(),
				"pantry_id": config.PantryID,
			},
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to update alert configuration")
	}

	return nil
}

// Delete deletes an alert configuration
func (r *AlertConfigurationRepository) Delete(id uuid.UUID) error {
	if err := r.db.Delete(&AlertConfigurationModel{}, "id = ?", id).Error; err != nil {
		logger.LogRepositoryError(err, "alert_configuration", "delete", logger.ErrorContext{
			EntityID: id.String(),
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to delete alert configuration")
	}

	return nil
}
