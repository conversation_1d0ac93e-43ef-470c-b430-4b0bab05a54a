package postgres

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// NotificationModel represents the database model for notifications
type NotificationModel struct {
	ID          uuid.UUID  `gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID  `gorm:"type:uuid;not null;index"`
	PantryID    *uuid.UUID `gorm:"type:uuid;index"`
	Type        string     `gorm:"type:varchar(50);not null;index"`
	Channel     string     `gorm:"type:varchar(20);not null"`
	Priority    string     `gorm:"type:varchar(10);not null"`
	Title       string     `gorm:"type:varchar(255);not null"`
	Message     string     `gorm:"type:text;not null"`
	Data        string     `gorm:"type:jsonb"` // JSON data
	Status      string     `gorm:"type:varchar(20);not null;default:'pending';index"`
	ScheduledAt *time.Time
	SentAt      *time.Time
	Error       *string    `gorm:"type:text"`
	CreatedAt   time.Time  `gorm:"not null"`
	UpdatedAt   time.Time  `gorm:"not null"`

	// Foreign key constraints
	User   UserModel    `gorm:"foreignKey:UserID;constraint:OnDelete:CASCADE"`
	Pantry *PantryModel `gorm:"foreignKey:PantryID;constraint:OnDelete:CASCADE"`
}

// TableName returns the table name for the model
func (NotificationModel) TableName() string {
	return "notifications"
}

// ToDomain converts the model to domain entity
func (m *NotificationModel) ToDomain() *domain.Notification {
	notification := &domain.Notification{
		ID:          m.ID,
		UserID:      m.UserID,
		PantryID:    m.PantryID,
		Type:        domain.NotificationType(m.Type),
		Channel:     domain.NotificationChannel(m.Channel),
		Priority:    domain.NotificationPriority(m.Priority),
		Title:       m.Title,
		Message:     m.Message,
		Status:      domain.NotificationStatus(m.Status),
		ScheduledAt: m.ScheduledAt,
		SentAt:      m.SentAt,
		Error:       m.Error,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
	}

	// Parse data
	if m.Data != "" {
		var data map[string]interface{}
		if err := json.Unmarshal([]byte(m.Data), &data); err == nil {
			notification.Data = data
		}
	}

	return notification
}

// FromDomain converts domain entity to model
func (m *NotificationModel) FromDomain(notification *domain.Notification) {
	m.ID = notification.ID
	m.UserID = notification.UserID
	m.PantryID = notification.PantryID
	m.Type = string(notification.Type)
	m.Channel = string(notification.Channel)
	m.Priority = string(notification.Priority)
	m.Title = notification.Title
	m.Message = notification.Message
	m.Status = string(notification.Status)
	m.ScheduledAt = notification.ScheduledAt
	m.SentAt = notification.SentAt
	m.Error = notification.Error
	m.CreatedAt = notification.CreatedAt
	m.UpdatedAt = notification.UpdatedAt

	// Serialize data
	if notification.Data != nil {
		if dataJSON, err := json.Marshal(notification.Data); err == nil {
			m.Data = string(dataJSON)
		}
	}
}

// NotificationRepository implements the notification repository interface
type NotificationRepository struct {
	db *gorm.DB
}

// NewNotificationRepository creates a new notification repository
func NewNotificationRepository(db *gorm.DB) domain.NotificationRepository {
	return &NotificationRepository{
		db: db,
	}
}

// Create creates a new notification
func (r *NotificationRepository) Create(notification *domain.Notification) error {
	model := &NotificationModel{}
	model.FromDomain(notification)

	if err := r.db.Create(model).Error; err != nil {
		logger.LogRepositoryError(err, "notification", "create", logger.ErrorContext{
			EntityID: notification.ID.String(),
			Additional: map[string]interface{}{
				"user_id": notification.UserID.String(),
				"type":    string(notification.Type),
				"channel": string(notification.Channel),
			},
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to create notification")
	}

	// Update the domain object with generated values
	*notification = *model.ToDomain()
	return nil
}

// GetByID retrieves a notification by ID
func (r *NotificationRepository) GetByID(id uuid.UUID) (*domain.Notification, error) {
	var model NotificationModel

	if err := r.db.Where("id = ?", id).First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New(errors.ErrCodeNotFound, "notification not found")
		}
		
		logger.LogRepositoryError(err, "notification", "get_by_id", logger.ErrorContext{
			EntityID: id.String(),
		})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get notification")
	}

	return model.ToDomain(), nil
}

// GetByUserID retrieves notifications by user ID with pagination
func (r *NotificationRepository) GetByUserID(userID uuid.UUID, page, limit int) ([]*domain.Notification, int64, error) {
	var models []NotificationModel
	var total int64

	// Count total
	if err := r.db.Model(&NotificationModel{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		logger.LogRepositoryError(err, "notification", "count_by_user", logger.ErrorContext{
			Additional: map[string]interface{}{
				"user_id": userID.String(),
			},
		})
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to count notifications")
	}

	// Get paginated results
	offset := (page - 1) * limit
	if err := r.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&models).Error; err != nil {
		
		logger.LogRepositoryError(err, "notification", "get_by_user_id", logger.ErrorContext{
			Additional: map[string]interface{}{
				"user_id": userID.String(),
				"page":    page,
				"limit":   limit,
			},
		})
		return nil, 0, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get notifications")
	}

	notifications := make([]*domain.Notification, len(models))
	for i, model := range models {
		notifications[i] = model.ToDomain()
	}

	return notifications, total, nil
}

// GetPendingNotifications retrieves pending notifications
func (r *NotificationRepository) GetPendingNotifications(limit int) ([]*domain.Notification, error) {
	var models []NotificationModel

	query := r.db.Where("status = ?", string(domain.NotificationStatusPending))
	
	// Include scheduled notifications that are ready to send
	query = query.Where("scheduled_at IS NULL OR scheduled_at <= ?", time.Now())
	
	if err := query.Order("created_at ASC").Limit(limit).Find(&models).Error; err != nil {
		logger.LogRepositoryError(err, "notification", "get_pending", logger.ErrorContext{
			Additional: map[string]interface{}{
				"limit": limit,
			},
		})
		return nil, errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to get pending notifications")
	}

	notifications := make([]*domain.Notification, len(models))
	for i, model := range models {
		notifications[i] = model.ToDomain()
	}

	return notifications, nil
}

// Update updates a notification
func (r *NotificationRepository) Update(notification *domain.Notification) error {
	model := &NotificationModel{}
	model.FromDomain(notification)

	if err := r.db.Save(model).Error; err != nil {
		logger.LogRepositoryError(err, "notification", "update", logger.ErrorContext{
			EntityID: notification.ID.String(),
			Additional: map[string]interface{}{
				"user_id": notification.UserID.String(),
				"status":  string(notification.Status),
			},
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to update notification")
	}

	return nil
}

// MarkAsSent marks a notification as sent
func (r *NotificationRepository) MarkAsSent(id uuid.UUID) error {
	now := time.Now()
	if err := r.db.Model(&NotificationModel{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     string(domain.NotificationStatusSent),
			"sent_at":    now,
			"updated_at": now,
		}).Error; err != nil {
		
		logger.LogRepositoryError(err, "notification", "mark_as_sent", logger.ErrorContext{
			EntityID: id.String(),
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to mark notification as sent")
	}

	return nil
}

// MarkAsFailed marks a notification as failed
func (r *NotificationRepository) MarkAsFailed(id uuid.UUID, errorMsg string) error {
	if err := r.db.Model(&NotificationModel{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":     string(domain.NotificationStatusFailed),
			"error":      errorMsg,
			"updated_at": time.Now(),
		}).Error; err != nil {
		
		logger.LogRepositoryError(err, "notification", "mark_as_failed", logger.ErrorContext{
			EntityID: id.String(),
			Additional: map[string]interface{}{
				"error_message": errorMsg,
			},
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to mark notification as failed")
	}

	return nil
}

// Delete deletes a notification
func (r *NotificationRepository) Delete(id uuid.UUID) error {
	if err := r.db.Delete(&NotificationModel{}, "id = ?", id).Error; err != nil {
		logger.LogRepositoryError(err, "notification", "delete", logger.ErrorContext{
			EntityID: id.String(),
		})
		return errors.Wrap(err, errors.ErrCodeDatabaseError, "failed to delete notification")
	}

	return nil
}
