-- Create alert_configurations table
CREATE TABLE alert_configurations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    enabled B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT false,
    warning_days INTEGER NOT NULL DEFAULT 7,
    alert_days INTEGER NOT NULL DEFAULT 3,
    critical_days INTEGER NOT NULL DEFAULT 1,
    channels JSONB NOT NULL DEFAULT '[]',
    quiet_hours JSONB,
    category_filters JSONB NOT NULL DEFAULT '[]',
    min_value DECIMAL(10,2),
    last_checked TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT alert_configurations_warning_days_check CHECK (warning_days >= 1 AND warning_days <= 30),
    CONSTRAINT alert_configurations_alert_days_check CHECK (alert_days >= 0 AND alert_days <= 7),
    CONSTRAINT alert_configurations_critical_days_check CHECK (critical_days >= 0 AND critical_days <= 1),
    CONSTRAINT alert_configurations_min_value_check CHECK (min_value IS NULL OR min_value >= 0),
    
    -- Unique constraint for user-pantry combination
    UNIQUE(user_id, pantry_id)
);

-- Create indexes for alert_configurations
CREATE INDEX idx_alert_configurations_user_id ON alert_configurations(user_id);
CREATE INDEX idx_alert_configurations_pantry_id ON alert_configurations(pantry_id);
CREATE INDEX idx_alert_configurations_enabled ON alert_configurations(enabled);
CREATE INDEX idx_alert_configurations_last_checked ON alert_configurations(last_checked);

-- Create notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    pantry_id UUID REFERENCES pantries(pantry_id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    channel VARCHAR(20) NOT NULL,
    priority VARCHAR(10) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT notifications_type_check CHECK (type IN (
        'expiration_warning',
        'expiration_alert', 
        'expiration_critical',
        'inventory_low_stock',
        'inventory_empty',
        'recipe_ready',
        'shopping_reminder'
    )),
    CONSTRAINT notifications_channel_check CHECK (channel IN (
        'email',
        'telegram',
        'in_app',
        'push',
        'sms'
    )),
    CONSTRAINT notifications_priority_check CHECK (priority IN (
        'low',
        'medium',
        'high',
        'critical'
    )),
    CONSTRAINT notifications_status_check CHECK (status IN (
        'pending',
        'sent',
        'failed',
        'skipped'
    ))
);

-- Create indexes for notifications
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_pantry_id ON notifications(pantry_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled_at ON notifications(scheduled_at);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- Create composite index for pending notifications query
CREATE INDEX idx_notifications_pending_ready ON notifications(status, scheduled_at) 
WHERE status = 'pending';

-- Add comments for documentation
COMMENT ON TABLE alert_configurations IS 'Stores expiration alert configuration for users and pantries';
COMMENT ON COLUMN alert_configurations.pantry_id IS 'NULL for global user configuration, specific pantry ID for pantry-specific configuration';
COMMENT ON COLUMN alert_configurations.channels IS 'JSON array of notification channels (email, telegram, etc.)';
COMMENT ON COLUMN alert_configurations.quiet_hours IS 'JSON object defining quiet hours when notifications should not be sent';
COMMENT ON COLUMN alert_configurations.category_filters IS 'JSON array of category UUIDs to filter alerts by';
COMMENT ON COLUMN alert_configurations.min_value IS 'Minimum item value to trigger alerts (optional filter)';

COMMENT ON TABLE notifications IS 'Stores all notifications sent to users';
COMMENT ON COLUMN notifications.data IS 'JSON data specific to the notification type';
COMMENT ON COLUMN notifications.scheduled_at IS 'When the notification should be sent (NULL for immediate)';
COMMENT ON COLUMN notifications.sent_at IS 'When the notification was actually sent';
COMMENT ON COLUMN notifications.error IS 'Error message if notification failed to send';
