package logger

import (
	"sync"
	"time"
)

var (
	globalErrorLogger *ErrorLogger
	globalLoggerMutex sync.RWMutex
)

// SetGlobalErrorLogger sets the global error logger instance
func SetGlobalErrorLogger(logger *Logger) {
	globalLoggerMutex.Lock()
	defer globalLoggerMutex.Unlock()
	globalErrorLogger = NewErrorLogger(logger)
}

// GetGlobalErrorLogger returns the global error logger instance
func GetGlobalErrorLogger() *ErrorLogger {
	globalLoggerMutex.RLock()
	defer globalLoggerMutex.RUnlock()
	return globalErrorLogger
}

// Global convenience functions for error logging

// LogError logs an error with context using the global logger
func LogError(err error, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogError(err, context)
	}
}

// LogRepositoryError logs repository errors using the global logger
func LogRepositoryError(err error, entity, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogRepositoryError(err, entity, operation, context)
	}
}

// LogUsecaseError logs use case errors using the global logger
func LogUsecaseError(err error, usecase, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogUsecaseError(err, usecase, operation, context)
	}
}

// LogServiceError logs service errors using the global logger
func LogServiceError(err error, service, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogServiceError(err, service, operation, context)
	}
}

// LogHandlerError logs handler errors using the global logger
func LogHandlerError(err error, handler, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogHandlerError(err, handler, operation, context)
	}
}

// LogInfrastructureError logs infrastructure errors using the global logger
func LogInfrastructureError(err error, component, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogInfrastructureError(err, component, operation, context)
	}
}

// LogDomainError logs domain errors using the global logger
func LogDomainError(err error, domain, operation string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogDomainError(err, domain, operation, context)
	}
}

// LogStartupError logs startup errors using the global logger
func LogStartupError(err error, phase string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogStartupError(err, phase, context)
	}
}

// LogConfigurationError logs configuration errors using the global logger
func LogConfigurationError(err error, configSection string, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogConfigurationError(err, configSection, context)
	}
}

// LogPerformanceError logs performance errors using the global logger
func LogPerformanceError(err error, operation string, duration time.Duration, threshold time.Duration, context ErrorContext) {
	if globalErrorLogger != nil {
		globalErrorLogger.LogPerformanceError(err, operation, duration, threshold, context)
	}
}

// Convenience functions with minimal context

// QuickLogError logs an error with minimal context
func QuickLogError(err error, component, operation string) {
	LogError(err, ErrorContext{
		Component: component,
		Operation: operation,
	})
}

// QuickLogRepositoryError logs a repository error with minimal context
func QuickLogRepositoryError(err error, entity, operation string) {
	LogRepositoryError(err, entity, operation, ErrorContext{})
}

// QuickLogUsecaseError logs a use case error with minimal context
func QuickLogUsecaseError(err error, usecase, operation string) {
	LogUsecaseError(err, usecase, operation, ErrorContext{})
}

// QuickLogServiceError logs a service error with minimal context
func QuickLogServiceError(err error, service, operation string) {
	LogServiceError(err, service, operation, ErrorContext{})
}

// QuickLogHandlerError logs a handler error with minimal context
func QuickLogHandlerError(err error, handler, operation string) {
	LogHandlerError(err, handler, operation, ErrorContext{})
}

// Context builders for common scenarios

// WithUserContext adds user context to error logging
func WithUserContext(userID, requestID string) ErrorContext {
	return ErrorContext{
		UserID:    userID,
		RequestID: requestID,
	}
}

// WithEntityContext adds entity context to error logging
func WithEntityContext(entity, entityID string) ErrorContext {
	return ErrorContext{
		Entity:   entity,
		EntityID: entityID,
	}
}

// WithPerformanceContext adds performance context to error logging
func WithPerformanceContext(duration time.Duration) ErrorContext {
	return ErrorContext{
		Duration: &duration,
	}
}

// WithAdditionalContext adds additional context to error logging
func WithAdditionalContext(additional map[string]interface{}) ErrorContext {
	return ErrorContext{
		Additional: additional,
	}
}

// CombineContexts combines multiple error contexts
func CombineContexts(contexts ...ErrorContext) ErrorContext {
	combined := ErrorContext{
		Additional: make(map[string]interface{}),
	}
	
	for _, ctx := range contexts {
		if ctx.Operation != "" {
			combined.Operation = ctx.Operation
		}
		if ctx.Component != "" {
			combined.Component = ctx.Component
		}
		if ctx.Layer != "" {
			combined.Layer = ctx.Layer
		}
		if ctx.Entity != "" {
			combined.Entity = ctx.Entity
		}
		if ctx.EntityID != "" {
			combined.EntityID = ctx.EntityID
		}
		if ctx.UserID != "" {
			combined.UserID = ctx.UserID
		}
		if ctx.RequestID != "" {
			combined.RequestID = ctx.RequestID
		}
		if ctx.Duration != nil {
			combined.Duration = ctx.Duration
		}
		if ctx.StackTrace != "" {
			combined.StackTrace = ctx.StackTrace
		}
		
		// Merge additional context
		if ctx.Additional != nil {
			for key, value := range ctx.Additional {
				combined.Additional[key] = value
			}
		}
	}
	
	return combined
}

// Specialized error logging functions

// LogDatabaseConnectionError logs database connection errors
func LogDatabaseConnectionError(err error, database string, context ErrorContext) {
	context.Additional = map[string]interface{}{
		"database": database,
		"error_type": "connection",
	}
	LogInfrastructureError(err, "database", "connect", context)
}

// LogCacheError logs cache operation errors
func LogCacheError(err error, operation, key string, context ErrorContext) {
	context.Additional = map[string]interface{}{
		"cache_key": key,
		"cache_operation": operation,
	}
	LogInfrastructureError(err, "cache", operation, context)
}

// LogFileSystemError logs file system errors
func LogFileSystemError(err error, operation, path string, context ErrorContext) {
	context.Additional = map[string]interface{}{
		"file_path": path,
		"fs_operation": operation,
	}
	LogInfrastructureError(err, "filesystem", operation, context)
}

// LogNetworkError logs network operation errors
func LogNetworkError(err error, operation, endpoint string, context ErrorContext) {
	context.Additional = map[string]interface{}{
		"endpoint": endpoint,
		"network_operation": operation,
	}
	LogInfrastructureError(err, "network", operation, context)
}

// LogMigrationError logs database migration errors
func LogMigrationError(err error, migration, direction string, context ErrorContext) {
	context.Additional = map[string]interface{}{
		"migration": migration,
		"direction": direction,
	}
	LogInfrastructureError(err, "migration", "execute", context)
}
