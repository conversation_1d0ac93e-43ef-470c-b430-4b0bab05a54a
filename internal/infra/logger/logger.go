package logger

import (
	"io"
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"

	"github.com/wongpinter/pantry-pal/internal/infra/config"
)

// Logger wraps zerolog.Logger with additional functionality
type Logger struct {
	*zerolog.Logger
}

// New creates a new logger instance based on configuration
func New(cfg config.LoggerConfig) *Logger {
	// Set global log level
	level := parseLogLevel(cfg.Level)
	zerolog.SetGlobalLevel(level)

	// Configure output format
	var output io.Writer = os.Stdout

	if cfg.Format == "console" {
		// Pretty console output for development
		output = zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: cfg.TimeFormat,
			NoColor:    false,
		}
	}

	// Configure time format
	if cfg.TimeFormat != "" {
		zerolog.TimeFieldFormat = cfg.TimeFormat
	}

	// Create logger
	logger := zerolog.New(output).
		Level(level).
		With().
		Timestamp().
		Caller().
		Logger()

	return &Logger{Logger: &logger}
}

// parseLogLevel converts string log level to zerolog.Level
func parseLogLevel(level string) zerolog.Level {
	switch strings.ToLower(level) {
	case "trace":
		return zerolog.TraceLevel
	case "debug":
		return zerolog.DebugLevel
	case "info":
		return zerolog.InfoLevel
	case "warn", "warning":
		return zerolog.WarnLevel
	case "error":
		return zerolog.ErrorLevel
	case "fatal":
		return zerolog.FatalLevel
	case "panic":
		return zerolog.PanicLevel
	default:
		return zerolog.InfoLevel
	}
}

// WithRequestID adds request ID to logger context
func (l *Logger) WithRequestID(requestID string) *Logger {
	logger := l.With().Str("request_id", requestID).Logger()
	return &Logger{Logger: &logger}
}

// WithUserID adds user ID to logger context
func (l *Logger) WithUserID(userID string) *Logger {
	logger := l.With().Str("user_id", userID).Logger()
	return &Logger{Logger: &logger}
}

// WithPantryID adds pantry ID to logger context
func (l *Logger) WithPantryID(pantryID string) *Logger {
	logger := l.With().Str("pantry_id", pantryID).Logger()
	return &Logger{Logger: &logger}
}

// WithComponent adds component name to logger context
func (l *Logger) WithComponent(component string) *Logger {
	logger := l.With().Str("component", component).Logger()
	return &Logger{Logger: &logger}
}

// WithFields adds multiple fields to logger context
func (l *Logger) WithFields(fields map[string]interface{}) *Logger {
	event := l.With()
	for key, value := range fields {
		event = event.Interface(key, value)
	}
	logger := event.Logger()
	return &Logger{Logger: &logger}
}

// LogHTTPRequest logs HTTP request details
func (l *Logger) LogHTTPRequest(method, path, userAgent, clientIP string, statusCode int, duration time.Duration) {
	l.Info().
		Str("method", method).
		Str("path", path).
		Str("user_agent", userAgent).
		Str("client_ip", clientIP).
		Int("status_code", statusCode).
		Dur("duration", duration).
		Msg("HTTP request processed")
}

// LogInfo logs info message with additional context
func (l *Logger) LogInfo(message string, fields map[string]interface{}) {
	event := l.Info()

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogWarn logs warning message with additional context
func (l *Logger) LogWarn(message string, fields map[string]interface{}) {
	event := l.Warn()

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogError logs error with additional context
func (l *Logger) LogError(err error, message string, fields map[string]interface{}) {
	event := l.Error().Err(err)

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogErrorWithCategory logs error with category and enhanced context
func (l *Logger) LogErrorWithCategory(err error, category, message string, fields map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", category)

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg(message)
}

// LogValidationError logs validation errors with field details
func (l *Logger) LogValidationError(err error, fields map[string]string, context map[string]interface{}) {
	event := l.Warn().
		Err(err).
		Str("error_category", "validation").
		Interface("validation_fields", fields)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Validation error occurred")
}

// LogBusinessError logs business logic errors
func (l *Logger) LogBusinessError(err error, rule string, context map[string]interface{}) {
	event := l.Warn().
		Err(err).
		Str("error_category", "business_logic").
		Str("business_rule", rule)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Business rule violation")
}

// LogSystemError logs system-level errors
func (l *Logger) LogSystemError(err error, component string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "system").
		Str("component", component)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("System error occurred")
}

// LogDatabaseQuery logs database query execution
func (l *Logger) LogDatabaseQuery(query string, duration time.Duration, rowsAffected int64) {
	l.Debug().
		Str("query", query).
		Dur("duration", duration).
		Int64("rows_affected", rowsAffected).
		Msg("Database query executed")
}

// LogBusinessEvent logs business domain events
func (l *Logger) LogBusinessEvent(eventType string, entityID string, fields map[string]interface{}) {
	event := l.Info().
		Str("event_type", eventType).
		Str("entity_id", entityID)

	if fields != nil {
		for key, value := range fields {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Business event occurred")
}

// LogSecurityEvent logs security-related events
func (l *Logger) LogSecurityEvent(eventType string, userID string, details map[string]interface{}) {
	event := l.Warn().
		Str("security_event", eventType).
		Str("user_id", userID)

	if details != nil {
		for key, value := range details {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Security event detected")
}

// LogDatabaseError logs database-related errors with query context
func (l *Logger) LogDatabaseError(err error, operation string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "database").
		Str("db_operation", operation)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Database error occurred")
}

// LogExternalServiceError logs external service errors
func (l *Logger) LogExternalServiceError(err error, service string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "external_service").
		Str("service_name", service)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("External service error occurred")
}

// LogAuthenticationError logs authentication-related errors
func (l *Logger) LogAuthenticationError(err error, context map[string]interface{}) {
	event := l.Warn().
		Err(err).
		Str("error_category", "authentication")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Authentication error occurred")
}

// LogAuthorizationError logs authorization-related errors
func (l *Logger) LogAuthorizationError(err error, resource string, context map[string]interface{}) {
	event := l.Warn().
		Err(err).
		Str("error_category", "authorization").
		Str("resource", resource)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Authorization error occurred")
}

// LogPerformanceIssue logs performance-related issues
func (l *Logger) LogPerformanceIssue(operation string, duration time.Duration, threshold time.Duration, context map[string]interface{}) {
	event := l.Warn().
		Str("issue_type", "performance").
		Str("operation", operation).
		Dur("duration", duration).
		Dur("threshold", threshold).
		Float64("slowness_factor", float64(duration)/float64(threshold))

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Performance threshold exceeded")
}

// LogRateLimitError logs rate limiting errors
func (l *Logger) LogRateLimitError(err error, limit int, window time.Duration, context map[string]interface{}) {
	event := l.Warn().
		Err(err).
		Str("error_category", "rate_limit").
		Int("limit", limit).
		Dur("window", window)

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Rate limit exceeded")
}

// LogRepositoryError logs repository/persistence layer errors
func (l *Logger) LogRepositoryError(err error, operation string, entity string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "repository").
		Str("operation", operation).
		Str("entity", entity).
		Str("layer", "persistence")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Repository operation failed")
}

// LogUsecaseError logs use case/business logic layer errors
func (l *Logger) LogUsecaseError(err error, usecase string, operation string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "usecase").
		Str("usecase", usecase).
		Str("operation", operation).
		Str("layer", "business")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Use case operation failed")
}

// LogServiceError logs service layer errors
func (l *Logger) LogServiceError(err error, service string, operation string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "service").
		Str("service", service).
		Str("operation", operation).
		Str("layer", "service")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Service operation failed")
}

// LogConfigurationError logs configuration-related errors
func (l *Logger) LogConfigurationError(err error, component string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "configuration").
		Str("component", component).
		Str("layer", "infrastructure")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Configuration error occurred")
}

// LogStartupError logs application startup errors
func (l *Logger) LogStartupError(err error, phase string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "startup").
		Str("phase", phase).
		Str("layer", "application")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Application startup failed")
}

// LogMigrationError logs database migration errors
func (l *Logger) LogMigrationError(err error, migration string, direction string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "migration").
		Str("migration", migration).
		Str("direction", direction).
		Str("layer", "database")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Database migration failed")
}

// LogCacheError logs cache-related errors
func (l *Logger) LogCacheError(err error, operation string, key string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "cache").
		Str("operation", operation).
		Str("cache_key", key).
		Str("layer", "infrastructure")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Cache operation failed")
}

// LogFileSystemError logs file system operation errors
func (l *Logger) LogFileSystemError(err error, operation string, path string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "filesystem").
		Str("operation", operation).
		Str("file_path", path).
		Str("layer", "infrastructure")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("File system operation failed")
}

// LogNetworkError logs network-related errors
func (l *Logger) LogNetworkError(err error, operation string, endpoint string, context map[string]interface{}) {
	event := l.Error().
		Err(err).
		Str("error_category", "network").
		Str("operation", operation).
		Str("endpoint", endpoint).
		Str("layer", "infrastructure")

	if context != nil {
		for key, value := range context {
			event = event.Interface(key, value)
		}
	}

	event.Msg("Network operation failed")
}

// SetGlobalLogger sets the global logger instance
func SetGlobalLogger(logger *Logger) {
	log.Logger = *logger.Logger
}
