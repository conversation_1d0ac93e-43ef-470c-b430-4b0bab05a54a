package logger

import (
	"fmt"
	"runtime"
	"strings"
	"time"

	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ErrorContext represents comprehensive error context for logging
type ErrorContext struct {
	// Core identification
	Operation string `json:"operation"`
	Component string `json:"component"`
	Layer     string `json:"layer"` // handler, usecase, repository, service, infrastructure

	// Entity context
	Entity   string `json:"entity,omitempty"`
	EntityID string `json:"entity_id,omitempty"`

	// User context
	UserID    string `json:"user_id,omitempty"`
	RequestID string `json:"request_id,omitempty"`

	// Additional context
	Additional map[string]interface{} `json:"additional,omitempty"`

	// Performance context
	Duration *time.Duration `json:"duration,omitempty"`

	// Stack trace (optional)
	StackTrace string `json:"stack_trace,omitempty"`
}

// ErrorLogger provides comprehensive error logging across all application layers
type ErrorLogger struct {
	logger *Logger
}

// NewErrorLogger creates a new error logger
func NewErrorLogger(logger *Logger) *ErrorLogger {
	return &ErrorLogger{
		logger: logger,
	}
}

// LogError logs an error with comprehensive context and automatic categorization
func (el *ErrorLogger) LogError(err error, context ErrorContext) {
	// Build logging context
	logContext := el.buildLogContext(context)

	// Add stack trace if not provided and in development
	if context.StackTrace == "" {
		logContext["stack_trace"] = el.getStackTrace(5)
	} else if context.StackTrace != "" {
		logContext["stack_trace"] = context.StackTrace
	}

	// Categorize and log based on error type
	if appErr := errors.GetAppError(err); appErr != nil {
		el.logAppError(appErr, context, logContext)
	} else {
		el.logSystemError(err, context, logContext)
	}
}

// LogRepositoryError logs repository layer errors with entity context
func (el *ErrorLogger) LogRepositoryError(err error, entity, operation string, context ErrorContext) {
	context.Layer = "repository"
	context.Entity = entity
	context.Operation = operation
	context.Component = fmt.Sprintf("%s_repository", entity)

	el.LogError(err, context)
}

// LogUsecaseError logs use case layer errors
func (el *ErrorLogger) LogUsecaseError(err error, usecase, operation string, context ErrorContext) {
	context.Layer = "usecase"
	context.Component = usecase
	context.Operation = operation

	el.LogError(err, context)
}

// LogServiceError logs service layer errors
func (el *ErrorLogger) LogServiceError(err error, service, operation string, context ErrorContext) {
	context.Layer = "service"
	context.Component = service
	context.Operation = operation

	el.LogError(err, context)
}

// LogHandlerError logs handler layer errors
func (el *ErrorLogger) LogHandlerError(err error, handler, operation string, context ErrorContext) {
	context.Layer = "handler"
	context.Component = handler
	context.Operation = operation

	el.LogError(err, context)
}

// LogInfrastructureError logs infrastructure layer errors
func (el *ErrorLogger) LogInfrastructureError(err error, component, operation string, context ErrorContext) {
	context.Layer = "infrastructure"
	context.Component = component
	context.Operation = operation

	el.LogError(err, context)
}

// LogDomainError logs domain layer errors
func (el *ErrorLogger) LogDomainError(err error, domain, operation string, context ErrorContext) {
	context.Layer = "domain"
	context.Component = domain
	context.Operation = operation

	el.LogError(err, context)
}

// LogStartupError logs application startup errors
func (el *ErrorLogger) LogStartupError(err error, phase string, context ErrorContext) {
	context.Layer = "application"
	context.Component = "startup"
	context.Operation = phase

	el.LogError(err, context)
}

// LogConfigurationError logs configuration errors
func (el *ErrorLogger) LogConfigurationError(err error, configSection string, context ErrorContext) {
	context.Layer = "configuration"
	context.Component = "config"
	context.Operation = fmt.Sprintf("load_%s", configSection)

	el.LogError(err, context)
}

// LogPerformanceError logs performance-related errors
func (el *ErrorLogger) LogPerformanceError(err error, operation string, duration time.Duration, threshold time.Duration, context ErrorContext) {
	context.Operation = operation
	context.Duration = &duration
	if context.Additional == nil {
		context.Additional = make(map[string]interface{})
	}
	context.Additional["threshold"] = threshold
	context.Additional["slowness_factor"] = float64(duration) / float64(threshold)
	context.Additional["performance_issue"] = true

	el.LogError(err, context)
}

// Helper methods

func (el *ErrorLogger) buildLogContext(context ErrorContext) map[string]interface{} {
	logContext := map[string]interface{}{
		"operation": context.Operation,
		"component": context.Component,
		"layer":     context.Layer,
	}

	if context.Entity != "" {
		logContext["entity"] = context.Entity
	}

	if context.EntityID != "" {
		logContext["entity_id"] = context.EntityID
	}

	if context.UserID != "" {
		logContext["user_id"] = context.UserID
	}

	if context.RequestID != "" {
		logContext["request_id"] = context.RequestID
	}

	if context.Duration != nil {
		logContext["duration_ms"] = context.Duration.Milliseconds()
	}

	// Add additional context
	if context.Additional != nil {
		for key, value := range context.Additional {
			logContext[key] = value
		}
	}

	return logContext
}

func (el *ErrorLogger) logAppError(appErr *errors.AppError, context ErrorContext, logContext map[string]interface{}) {
	// Add error-specific context
	logContext["error_code"] = string(appErr.Code)
	logContext["http_status"] = appErr.HTTPStatus

	if appErr.Details != nil {
		logContext["error_details"] = appErr.Details
	}

	if appErr.Cause != nil {
		logContext["underlying_error"] = appErr.Cause.Error()
	}

	// Log based on error category
	switch appErr.Code {
	case errors.ErrCodeValidationFailed, errors.ErrCodeInvalidInput, errors.ErrCodeMissingRequiredField:
		el.logger.LogValidationError(appErr, extractValidationFields(appErr), logContext)

	case errors.ErrCodeDatabaseError:
		operation := context.Operation
		if context.Entity != "" {
			operation = fmt.Sprintf("%s_%s", context.Entity, operation)
		}
		el.logger.LogDatabaseError(appErr, operation, logContext)

	case errors.ErrCodeExternalServiceError:
		serviceName := context.Component
		if appErr.Details != nil {
			if service, ok := appErr.Details["service"].(string); ok {
				serviceName = service
			}
		}
		el.logger.LogExternalServiceError(appErr, serviceName, logContext)

	case errors.ErrCodeInvalidCredentials, errors.ErrCodeTokenExpired, errors.ErrCodeTokenInvalid, errors.ErrCodeUnauthorized:
		el.logger.LogAuthenticationError(appErr, logContext)

	case errors.ErrCodeForbidden, errors.ErrCodeInsufficientPermissions:
		resource := context.Entity
		if resource == "" {
			resource = context.Component
		}
		el.logger.LogAuthorizationError(appErr, resource, logContext)

	case errors.ErrCodeBusinessRuleViolation:
		rule := "unknown"
		if appErr.Details != nil {
			if r, ok := appErr.Details["rule"].(string); ok {
				rule = r
			}
		}
		el.logger.LogBusinessError(appErr, rule, logContext)

	default:
		category := string(appErr.Code)
		message := fmt.Sprintf("%s error in %s.%s", category, context.Component, context.Operation)
		el.logger.LogErrorWithCategory(appErr, category, message, logContext)
	}
}

func (el *ErrorLogger) logSystemError(err error, context ErrorContext, logContext map[string]interface{}) {
	// Categorize system errors
	errStr := err.Error()
	category := "system"

	switch {
	case strings.Contains(errStr, "connection refused"), strings.Contains(errStr, "no route to host"):
		category = "connection"
	case strings.Contains(errStr, "timeout"), strings.Contains(errStr, "deadline exceeded"):
		category = "timeout"
	case strings.Contains(errStr, "context canceled"):
		category = "canceled"
	case strings.Contains(errStr, "no such file"), strings.Contains(errStr, "file not found"):
		category = "file_not_found"
	case strings.Contains(errStr, "permission denied"), strings.Contains(errStr, "access denied"):
		category = "permission"
	case strings.Contains(errStr, "out of memory"), strings.Contains(errStr, "no space left"):
		category = "resource"
	}

	logContext["error_category"] = category
	logContext["system_error"] = true

	el.logger.LogSystemError(err, context.Component, logContext)
}

func (el *ErrorLogger) getStackTrace(skip int) string {
	var stackTrace strings.Builder

	for i := skip; i < skip+10; i++ {
		pc, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}

		fn := runtime.FuncForPC(pc)
		if fn == nil {
			continue
		}

		// Format: function_name (file:line)
		stackTrace.WriteString(fmt.Sprintf("%s (%s:%d)\n", fn.Name(), file, line))
	}

	return stackTrace.String()
}

func extractValidationFields(appErr *errors.AppError) map[string]string {
	if appErr.Details == nil {
		return nil
	}

	if fields, ok := appErr.Details["fields"].(map[string]string); ok {
		return fields
	}

	return nil
}
