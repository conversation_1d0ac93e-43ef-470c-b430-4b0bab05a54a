package usecases

import (
	"context"
	"fmt"
	"strings"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
	"github.com/wongpinter/pantry-pal/internal/infra/logger"
)

// RecipeUsecase handles recipe business logic
type RecipeUsecase struct {
	recipeRepo           domain.RecipeRepository
	recipeTagRepo        domain.RecipeTagRepository
	recipeReviewRepo     domain.RecipeReviewRepository
	recipeCollectionRepo domain.RecipeCollectionRepository
	inventoryRepo        domain.InventoryItemRepository
	productVariantRepo   domain.ProductVariantRepository
	productRepo          domain.ProductRepository
	unitRepo             domain.UnitOfMeasureRepository
	pantryAuthzService   domain.PantryAuthorizationService
	logger               *logger.Logger
}

// NewRecipeUsecase creates a new recipe usecase
func NewRecipeUsecase(
	recipeRepo domain.RecipeRepository,
	recipeTagRepo domain.RecipeTagRepository,
	recipeReviewRepo domain.RecipeReviewRepository,
	recipeCollectionRepo domain.RecipeCollectionRepository,
	inventoryRepo domain.InventoryItemRepository,
	productVariantRepo domain.ProductVariantRepository,
	productRepo domain.ProductRepository,
	unitRepo domain.UnitOfMeasureRepository,
	pantryAuthzService domain.PantryAuthorizationService,
	log *logger.Logger,
) *RecipeUsecase {
	return &RecipeUsecase{
		recipeRepo:           recipeRepo,
		recipeTagRepo:        recipeTagRepo,
		recipeReviewRepo:     recipeReviewRepo,
		recipeCollectionRepo: recipeCollectionRepo,
		inventoryRepo:        inventoryRepo,
		productVariantRepo:   productVariantRepo,
		productRepo:          productRepo,
		unitRepo:             unitRepo,
		pantryAuthzService:   pantryAuthzService,
		logger:               log,
	}
}

// CreateRecipe creates a new recipe
func (uc *RecipeUsecase) CreateRecipe(ctx context.Context, userID uuid.UUID, req *domain.CreateRecipeRequest) (*domain.Recipe, error) {
	// Create recipe
	recipe := domain.NewRecipe(userID, req)

	// Save recipe
	if err := uc.recipeRepo.Create(recipe); err != nil {
		uc.logger.LogError(err, "Failed to create recipe", map[string]interface{}{
			"user_id": userID.String(),
			"title":   req.Title,
		})
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to create recipe")
	}

	// Process tags
	if len(req.Tags) > 0 {
		if err := uc.processTags(recipe.ID, req.Tags); err != nil {
			uc.logger.LogError(err, "Failed to process recipe tags", map[string]interface{}{
				"recipe_id": recipe.ID.String(),
				"tags":      req.Tags,
			})
			// Don't fail the recipe creation for tag processing errors
		}
	}

	uc.logger.LogBusinessEvent("recipe.created", recipe.ID.String(), map[string]interface{}{
		"user_id":     userID.String(),
		"title":       recipe.Title,
		"servings":    recipe.Servings,
		"difficulty":  recipe.Difficulty,
		"is_public":   recipe.IsPublic,
		"ingredients": len(recipe.Ingredients),
		"steps":       len(recipe.Instructions),
	})

	return recipe, nil
}

// GetRecipe retrieves a recipe by ID
func (uc *RecipeUsecase) GetRecipe(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID) (*domain.Recipe, error) {
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return nil, errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check if user can view this recipe
	if !recipe.CanBeViewedBy(userID) {
		return nil, errors.ErrForbidden
	}

	return recipe, nil
}

// GetUserRecipes retrieves recipes for a user
func (uc *RecipeUsecase) GetUserRecipes(ctx context.Context, userID uuid.UUID, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	// Set default pagination
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 10
	}
	if params.Limit > 100 {
		params.Limit = 100
	}

	recipes, total, err := uc.recipeRepo.GetByUserID(userID, params)
	if err != nil {
		return nil, 0, errors.New(errors.ErrCodeInternalError, "Failed to get user recipes")
	}

	return recipes, total, nil
}

// GetPublicRecipes retrieves public recipes
func (uc *RecipeUsecase) GetPublicRecipes(ctx context.Context, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	// Set default pagination
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 10
	}
	if params.Limit > 100 {
		params.Limit = 100
	}

	recipes, total, err := uc.recipeRepo.GetPublicRecipes(params)
	if err != nil {
		return nil, 0, errors.New(errors.ErrCodeInternalError, "Failed to get public recipes")
	}

	return recipes, total, nil
}

// UpdateRecipe updates a recipe
func (uc *RecipeUsecase) UpdateRecipe(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID, req *domain.UpdateRecipeRequest) (*domain.Recipe, error) {
	// Get existing recipe
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return nil, errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check ownership
	if !recipe.IsOwnedBy(userID) {
		return nil, errors.ErrForbidden
	}

	// Update recipe
	recipe.Update(req)

	// Save changes
	if err := uc.recipeRepo.Update(recipe); err != nil {
		uc.logger.LogError(err, "Failed to update recipe", map[string]interface{}{
			"user_id":   userID.String(),
			"recipe_id": recipeID.String(),
		})
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to update recipe")
	}

	// Process tags if provided
	if req.Tags != nil {
		if err := uc.processTags(recipe.ID, req.Tags); err != nil {
			uc.logger.LogError(err, "Failed to process updated recipe tags", map[string]interface{}{
				"recipe_id": recipe.ID.String(),
				"tags":      req.Tags,
			})
		}
	}

	uc.logger.LogBusinessEvent("recipe.updated", recipe.ID.String(), map[string]interface{}{
		"user_id":   userID.String(),
		"recipe_id": recipeID.String(),
		"title":     recipe.Title,
	})

	return recipe, nil
}

// DeleteRecipe deletes a recipe
func (uc *RecipeUsecase) DeleteRecipe(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID) error {
	// Get existing recipe
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check ownership
	if !recipe.IsOwnedBy(userID) {
		return errors.ErrForbidden
	}

	// Delete recipe
	if err := uc.recipeRepo.Delete(recipeID); err != nil {
		uc.logger.LogError(err, "Failed to delete recipe", map[string]interface{}{
			"user_id":   userID.String(),
			"recipe_id": recipeID.String(),
		})
		return errors.New(errors.ErrCodeInternalError, "Failed to delete recipe")
	}

	uc.logger.LogBusinessEvent("recipe.deleted", recipeID.String(), map[string]interface{}{
		"user_id":   userID.String(),
		"recipe_id": recipeID.String(),
		"title":     recipe.Title,
	})

	return nil
}

// ScaleRecipe scales a recipe to a different number of servings
func (uc *RecipeUsecase) ScaleRecipe(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID, req *domain.ScaleRecipeRequest) (*domain.Recipe, error) {
	// Get recipe
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return nil, errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check if user can view this recipe
	if !recipe.CanBeViewedBy(userID) {
		return nil, errors.ErrForbidden
	}

	// Create a copy with scaled ingredients
	scaledRecipe := *recipe
	scaledRecipe.Ingredients = recipe.ScaleIngredients(req.Servings)
	scaledRecipe.Servings = req.Servings

	// Recalculate nutrition if available
	if recipe.Nutrition != nil && recipe.Servings > 0 {
		scaleFactor := float64(req.Servings) / float64(recipe.Servings)
		scaledNutrition := *recipe.Nutrition

		if scaledNutrition.Calories != nil {
			calories := int(float64(*scaledNutrition.Calories) * scaleFactor)
			scaledNutrition.Calories = &calories
		}
		if scaledNutrition.Protein != nil {
			protein := *scaledNutrition.Protein * scaleFactor
			scaledNutrition.Protein = &protein
		}
		if scaledNutrition.Carbohydrates != nil {
			carbs := *scaledNutrition.Carbohydrates * scaleFactor
			scaledNutrition.Carbohydrates = &carbs
		}
		if scaledNutrition.Fat != nil {
			fat := *scaledNutrition.Fat * scaleFactor
			scaledNutrition.Fat = &fat
		}

		scaledRecipe.Nutrition = &scaledNutrition
	}

	return &scaledRecipe, nil
}

// MarkAsCooked marks a recipe as cooked
func (uc *RecipeUsecase) MarkAsCooked(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID) error {
	// Get recipe
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check if user can view this recipe
	if !recipe.CanBeViewedBy(userID) {
		return errors.ErrForbidden
	}

	// Update cook count and last cooked date
	if err := uc.recipeRepo.IncrementCookCount(recipeID); err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to increment cook count")
	}

	if err := uc.recipeRepo.UpdateLastCookedAt(recipeID); err != nil {
		return errors.New(errors.ErrCodeInternalError, "Failed to update last cooked date")
	}

	uc.logger.LogBusinessEvent("recipe.cooked", recipeID.String(), map[string]interface{}{
		"user_id":   userID.String(),
		"recipe_id": recipeID.String(),
		"title":     recipe.Title,
	})

	return nil
}

// SearchRecipes searches for recipes
func (uc *RecipeUsecase) SearchRecipes(ctx context.Context, userID uuid.UUID, query string, params domain.RecipeQueryParams) ([]*domain.Recipe, int64, error) {
	// Set default pagination
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.Limit <= 0 {
		params.Limit = 10
	}
	if params.Limit > 100 {
		params.Limit = 100
	}

	recipes, total, err := uc.recipeRepo.Search(query, params)
	if err != nil {
		return nil, 0, errors.New(errors.ErrCodeInternalError, "Failed to search recipes")
	}

	// Filter out private recipes that don't belong to the user
	filteredRecipes := make([]*domain.Recipe, 0, len(recipes))
	for _, recipe := range recipes {
		if recipe.CanBeViewedBy(userID) {
			filteredRecipes = append(filteredRecipes, recipe)
		}
	}

	return filteredRecipes, total, nil
}

// Helper methods

func (uc *RecipeUsecase) processTags(_ uuid.UUID, tagNames []string) error {
	for _, tagName := range tagNames {
		tagName = strings.TrimSpace(strings.ToLower(tagName))
		if tagName == "" {
			continue
		}

		// Try to get existing tag
		tag, err := uc.recipeTagRepo.GetByName(tagName)
		if err != nil {
			// Create new tag if it doesn't exist
			tag = domain.NewRecipeTag(tagName, fmt.Sprintf("Tag for %s recipes", tagName), nil, nil)
			if err := uc.recipeTagRepo.Create(tag); err != nil {
				uc.logger.LogError(err, "Failed to create recipe tag", map[string]interface{}{
					"tag_name": tagName,
				})
				continue
			}
		}

		// Increment tag usage
		if err := uc.recipeTagRepo.IncrementUsage(tag.ID); err != nil {
			uc.logger.LogError(err, "Failed to increment tag usage", map[string]interface{}{
				"tag_id":   tag.ID.String(),
				"tag_name": tagName,
			})
		}
	}

	return nil
}

// CheckInventoryAvailability checks if ingredients are available in pantry inventory
func (uc *RecipeUsecase) CheckInventoryAvailability(ctx context.Context, userID uuid.UUID, recipeID uuid.UUID, req *domain.InventoryCheckRequest) (*domain.InventoryCheckResponse, error) {
	// Get recipe
	recipe, err := uc.recipeRepo.GetByID(recipeID)
	if err != nil {
		return nil, errors.New(errors.ErrCodeNotFound, "Failed to get recipe")
	}

	// Check if user can view this recipe
	if !recipe.CanBeViewedBy(userID) {
		return nil, errors.ErrForbidden
	}

	// If no pantry specified, we can't check inventory
	if req.PantryID == nil {
		return &domain.InventoryCheckResponse{
			AvailableIngredients: []domain.IngredientAvailability{},
			MissingIngredients:   []domain.IngredientAvailability{},
			CanCook:              false,
			MissingCount:         len(recipe.Ingredients),
			TotalIngredients:     len(recipe.Ingredients),
		}, nil
	}

	// Check pantry access
	canView, err := uc.pantryAuthzService.CheckPermission(userID, *req.PantryID, domain.PermissionViewPantry)
	if err != nil {
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to check pantry permission")
	}
	if !canView {
		return nil, errors.ErrForbidden
	}

	// Get pantry inventory
	inventoryItems, _, err := uc.inventoryRepo.GetByPantryID(*req.PantryID, 1, 1000) // Get all items
	if err != nil {
		return nil, errors.New(errors.ErrCodeInternalError, "Failed to get pantry inventory")
	}

	// Create inventory map for quick lookup
	inventoryMap := make(map[uuid.UUID]*domain.InventoryItem)
	for _, item := range inventoryItems {
		inventoryMap[item.ProductVariantID] = item
	}

	var availableIngredients []domain.IngredientAvailability
	var missingIngredients []domain.IngredientAvailability
	var shoppingListItems []domain.ShoppingListItem

	// Check each ingredient
	for _, ingredient := range recipe.Ingredients {
		availability := domain.IngredientAvailability{
			IngredientID:     ingredient.ID,
			Name:             ingredient.Name,
			RequiredQuantity: ingredient.Quantity,
			RequiredUnit:     uc.getIngredientUnit(ingredient),
			IsOptional:       ingredient.IsOptional,
			ProductVariantID: ingredient.ProductVariantID,
		}

		// Check if ingredient is available in inventory
		if ingredient.ProductVariantID != nil {
			if inventoryItem, exists := inventoryMap[*ingredient.ProductVariantID]; exists {
				// Unit conversion implementation placeholder
				// Production implementation should:
				// 1. Compare ingredient unit with inventory item unit
				// 2. Convert quantities to common base unit if different
				// 3. Handle unit compatibility (weight vs volume vs count)
				// 4. Use conversion factors from unit_conversions table
				// 5. Provide meaningful error messages for incompatible units
				//
				// For now, assume units match and compare quantities directly
				availability.AvailableQuantity = &inventoryItem.Quantity
				availability.AvailableUnit = uc.getInventoryItemUnit(inventoryItem)
				availability.IsAvailable = inventoryItem.Quantity >= ingredient.Quantity
			}
		}

		if availability.IsAvailable {
			availableIngredients = append(availableIngredients, availability)
		} else {
			missingIngredients = append(missingIngredients, availability)

			// Create shopping list item for missing ingredient
			if ingredient.ProductVariantID != nil {
				// Get product variant details to get category and unit information
				variant, err := uc.productVariantRepo.GetByID(*ingredient.ProductVariantID)
				if err != nil {
					uc.logger.LogError(err, "Failed to get product variant for shopping list", map[string]interface{}{
						"variant_id": ingredient.ProductVariantID.String(),
					})
					continue
				}

				// Get product details
				product, err := uc.productRepo.GetByID(variant.ProductID)
				if err != nil {
					uc.logger.LogError(err, "Failed to get product for shopping list", map[string]interface{}{
						"product_id": variant.ProductID.String(),
					})
					continue
				}

				// Get unit information if available
				var unitID uuid.UUID
				var unitName, unitSymbol string
				if ingredient.UnitOfMeasureID != nil {
					unit, err := uc.unitRepo.GetByID(*ingredient.UnitOfMeasureID)
					if err == nil {
						unitID = unit.ID
						unitName = unit.Name
						unitSymbol = unit.Symbol
					} else {
						// Fallback to default unit
						unitName = uc.getIngredientUnit(ingredient)
						unitSymbol = unitName
					}
				} else {
					// Use ingredient unit or default
					unitName = uc.getIngredientUnit(ingredient)
					unitSymbol = unitName
				}

				// Create a shopping list item for the response
				shoppingListItem := domain.ShoppingListItem{
					ProductVariantID:  *ingredient.ProductVariantID,
					ProductName:       product.Name,
					VariantName:       variant.Name,
					CategoryID:        product.CategoryID,
					CategoryName:      "Recipe Ingredient", // We'd need to fetch category name separately
					SuggestedQuantity: ingredient.Quantity,
					UnitOfMeasureID:   unitID,
					UnitName:          unitName,
					UnitSymbol:        unitSymbol,
					CurrentQuantity:   0,
					Reasons: []domain.ShoppingListReason{
						{
							Type:        "recipe_required",
							Description: fmt.Sprintf("Required for recipe: %s", recipe.Title),
							Details: map[string]interface{}{
								"recipe_id":   recipe.ID.String(),
								"recipe_name": recipe.Title,
								"quantity":    ingredient.Quantity,
							},
						},
					},
					Priority:       domain.ShoppingListPriorityMedium,
					EstimatedPrice: nil,
				}
				shoppingListItems = append(shoppingListItems, shoppingListItem)
			}
		}
	}

	// Calculate if recipe can be cooked (all non-optional ingredients available)
	canCook := true
	missingCount := 0
	for _, missing := range missingIngredients {
		if !missing.IsOptional {
			canCook = false
			missingCount++
		}
	}

	response := &domain.InventoryCheckResponse{
		AvailableIngredients: availableIngredients,
		MissingIngredients:   missingIngredients,
		ShoppingListItems:    shoppingListItems,
		CanCook:              canCook,
		MissingCount:         missingCount,
		TotalIngredients:     len(recipe.Ingredients),
	}

	uc.logger.LogBusinessEvent("recipe.inventory_checked", recipeID.String(), map[string]interface{}{
		"user_id":           userID.String(),
		"recipe_id":         recipeID.String(),
		"pantry_id":         req.PantryID.String(),
		"can_cook":          canCook,
		"missing_count":     missingCount,
		"total_ingredients": len(recipe.Ingredients),
		"available_count":   len(availableIngredients),
	})

	return response, nil
}

// Helper methods for ingredient and inventory item units
func (uc *RecipeUsecase) getIngredientUnit(ingredient domain.RecipeIngredient) string {
	if ingredient.Unit != nil {
		return *ingredient.Unit
	}
	// Get unit from UnitOfMeasure if UnitOfMeasureID is set
	if ingredient.UnitOfMeasureID != nil {
		unit, err := uc.unitRepo.GetByID(*ingredient.UnitOfMeasureID)
		if err == nil {
			return unit.Symbol
		}
	}
	return "unit"
}

func (uc *RecipeUsecase) getInventoryItemUnit(item *domain.InventoryItem) *string {
	// Get unit from UnitOfMeasure if UnitOfMeasureID is set
	if item.UnitOfMeasureID != uuid.Nil {
		unit, err := uc.unitRepo.GetByID(item.UnitOfMeasureID)
		if err == nil {
			return &unit.Symbol
		}
	}
	unit := "unit"
	return &unit
}
