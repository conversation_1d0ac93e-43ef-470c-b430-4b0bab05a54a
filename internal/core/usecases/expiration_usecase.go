package usecases

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
	"github.com/wongpinter/pantry-pal/internal/infra/errors"
)

// ExpirationUsecase handles expiration tracking and alert business logic
type ExpirationUsecase struct {
	inventoryRepo       domain.InventoryItemRepository
	pantryRepo          domain.PantryRepository
	pantryLocationRepo  domain.PantryLocationRepository
	productVariantRepo  domain.ProductVariantRepository
	productRepo         domain.ProductRepository
	unitRepo            domain.UnitOfMeasureRepository
	alertConfigRepo     AlertConfigurationRepository
	authzService        domain.PantryAuthorizationService
	notificationService domain.NotificationService
	logger              Logger
}

// AlertConfigurationRepository defines the interface for alert configuration persistence
type AlertConfigurationRepository interface {
	Create(config *domain.AlertConfiguration) error
	GetByID(id uuid.UUID) (*domain.AlertConfiguration, error)
	GetByUserAndPantry(userID uuid.UUID, pantryID *uuid.UUID) (*domain.AlertConfiguration, error)
	GetActiveConfigurations() ([]*domain.AlertConfiguration, error)
	Update(config *domain.AlertConfiguration) error
	Delete(id uuid.UUID) error
}

// NewExpirationUsecase creates a new expiration use case
func NewExpirationUsecase(
	inventoryRepo domain.InventoryItemRepository,
	pantryRepo domain.PantryRepository,
	pantryLocationRepo domain.PantryLocationRepository,
	productVariantRepo domain.ProductVariantRepository,
	productRepo domain.ProductRepository,
	unitRepo domain.UnitOfMeasureRepository,
	alertConfigRepo AlertConfigurationRepository,
	authzService domain.PantryAuthorizationService,
	notificationService domain.NotificationService,
	logger Logger,
) *ExpirationUsecase {
	return &ExpirationUsecase{
		inventoryRepo:       inventoryRepo,
		pantryRepo:          pantryRepo,
		pantryLocationRepo:  pantryLocationRepo,
		productVariantRepo:  productVariantRepo,
		productRepo:         productRepo,
		unitRepo:            unitRepo,
		alertConfigRepo:     alertConfigRepo,
		authzService:        authzService,
		notificationService: notificationService,
		logger:              logger,
	}
}

// TrackExpiringItems tracks expiring items in a pantry and optionally sends alerts
func (uc *ExpirationUsecase) TrackExpiringItems(ctx context.Context, userID, pantryID uuid.UUID, req *domain.ExpirationTrackingRequest) (*domain.ExpirationTrackingResponse, error) {
	// Check if user can view items in this pantry
	canView, err := uc.authzService.CheckPermission(userID, pantryID, domain.PermissionViewItems)
	if err != nil {
		uc.logger.Error("Failed to check view items permission for expiration tracking", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, errors.ErrInternalError
	}

	if !canView {
		return nil, errors.ErrForbidden
	}

	// Validate pantry exists
	pantry, err := uc.pantryRepo.GetByID(pantryID)
	if err != nil {
		return nil, err
	}

	response := &domain.ExpirationTrackingResponse{
		Items:     make([]domain.ExpiringInventoryItem, 0),
		CheckedAt: time.Now().Format("2006-01-02T15:04:05Z07:00"),
	}

	// Get all inventory items for the pantry
	allItems, _, err := uc.inventoryRepo.GetByPantryID(pantryID, 1, 10000) // Get up to 10k items
	if err != nil {
		return nil, err
	}

	// Filter items with expiration dates
	itemsWithExpiration := make([]*domain.InventoryItem, 0)
	for _, item := range allItems {
		if item.ExpirationDate != nil && item.DeletedAt == nil && item.Quantity > 0 {
			// Apply category filter if specified
			if len(req.CategoryIDs) > 0 {
				// Check item category through product-variant-product relationship
				variant, err := uc.productVariantRepo.GetByID(item.ProductVariantID)
				if err != nil {
					uc.logger.Error("Failed to get product variant for category filtering", err, map[string]interface{}{
						"item_id":    item.ID,
						"variant_id": item.ProductVariantID,
					})
					continue
				}

				product, err := uc.productRepo.GetByID(variant.ProductID)
				if err != nil {
					uc.logger.Error("Failed to get product for category filtering", err, map[string]interface{}{
						"item_id":    item.ID,
						"product_id": variant.ProductID,
					})
					continue
				}

				// Check if product's category is in the filter list
				categoryMatches := false
				for _, categoryID := range req.CategoryIDs {
					if product.CategoryID == categoryID {
						categoryMatches = true
						break
					}
				}

				if !categoryMatches {
					continue // Skip this item as it doesn't match category filter
				}
			}
			itemsWithExpiration = append(itemsWithExpiration, item)
		}
	}

	// Analyze expiration status for each item
	now := time.Now()
	var notifications []*domain.Notification

	for _, item := range itemsWithExpiration {
		expiringItem, status := uc.analyzeItemExpiration(item, req, now)
		if status != "" {
			// Enrich with product details
			enrichedItem, err := uc.enrichExpiringItem(expiringItem)
			if err != nil {
				uc.logger.Error("Failed to enrich expiring item", err, map[string]interface{}{
					"item_id": item.ID,
				})
				continue
			}

			response.Items = append(response.Items, *enrichedItem)

			// Count by status
			switch status {
			case domain.ExpirationStatusWarning:
				response.WarningItems++
			case domain.ExpirationStatusAlert:
				response.AlertItems++
			case domain.ExpirationStatusCritical:
				response.CriticalItems++
			case domain.ExpirationStatusExpired:
				response.ExpiredItems++
			}

			// Create notifications if requested
			if req.SendAlerts && len(req.Channels) > 0 {
				itemNotifications := uc.createExpirationNotifications(userID, pantryID, pantry.Name, enrichedItem, req.Channels)
				notifications = append(notifications, itemNotifications...)
			}
		}
	}

	response.TotalItems = len(response.Items)
	response.Summary = uc.calculateExpirationSummary(response.Items)

	// Send notifications if any
	if len(notifications) > 0 {
		err := uc.notificationService.SendBulkNotifications(ctx, notifications)
		if err != nil {
			uc.logger.Error("Failed to send expiration notifications", err, map[string]interface{}{
				"user_id":            userID,
				"pantry_id":          pantryID,
				"notification_count": len(notifications),
			})
		} else {
			response.AlertsSent = len(notifications)
		}
	}

	uc.logger.Info("Expiration tracking completed", map[string]interface{}{
		"user_id":        userID,
		"pantry_id":      pantryID,
		"total_items":    response.TotalItems,
		"warning_items":  response.WarningItems,
		"alert_items":    response.AlertItems,
		"critical_items": response.CriticalItems,
		"expired_items":  response.ExpiredItems,
		"alerts_sent":    response.AlertsSent,
	})

	return response, nil
}

// ConfigureAlerts configures expiration alerts for a user/pantry
func (uc *ExpirationUsecase) ConfigureAlerts(ctx context.Context, userID uuid.UUID, pantryID *uuid.UUID, req *domain.AlertConfigurationRequest) (*domain.AlertConfiguration, error) {
	// Check permissions if pantry-specific
	if pantryID != nil {
		canManage, err := uc.authzService.CheckPermission(userID, *pantryID, domain.PermissionEditPantry)
		if err != nil {
			uc.logger.Error("Failed to check edit pantry permission", err, map[string]interface{}{
				"user_id":   userID,
				"pantry_id": *pantryID,
			})
			return nil, errors.ErrInternalError
		}

		if !canManage {
			return nil, errors.ErrForbidden
		}

		// Validate pantry exists
		_, err = uc.pantryRepo.GetByID(*pantryID)
		if err != nil {
			return nil, err
		}
	}

	// Check if configuration already exists
	existingConfig, err := uc.alertConfigRepo.GetByUserAndPantry(userID, pantryID)
	if err != nil && !uc.isNotFoundError(err) {
		return nil, err
	}

	var config *domain.AlertConfiguration
	if existingConfig != nil {
		// Update existing configuration
		existingConfig.UpdateConfiguration(req)
		config = existingConfig
		err = uc.alertConfigRepo.Update(config)
	} else {
		// Create new configuration
		config = domain.NewAlertConfiguration(userID, pantryID, req)
		err = uc.alertConfigRepo.Create(config)
	}

	if err != nil {
		uc.logger.Error("Failed to save alert configuration", err, map[string]interface{}{
			"user_id":   userID,
			"pantry_id": pantryID,
		})
		return nil, err
	}

	uc.logger.Info("Alert configuration saved", map[string]interface{}{
		"user_id":       userID,
		"pantry_id":     pantryID,
		"config_id":     config.ID,
		"enabled":       config.Enabled,
		"warning_days":  config.WarningDays,
		"alert_days":    config.AlertDays,
		"critical_days": config.CriticalDays,
		"channels":      config.Channels,
	})

	return config, nil
}

// GetAlertConfiguration retrieves alert configuration for a user/pantry
func (uc *ExpirationUsecase) GetAlertConfiguration(ctx context.Context, userID uuid.UUID, pantryID *uuid.UUID) (*domain.AlertConfiguration, error) {
	// Check permissions if pantry-specific
	if pantryID != nil {
		canView, err := uc.authzService.CheckPermission(userID, *pantryID, domain.PermissionViewItems)
		if err != nil {
			return nil, errors.ErrInternalError
		}

		if !canView {
			return nil, errors.ErrForbidden
		}
	}

	config, err := uc.alertConfigRepo.GetByUserAndPantry(userID, pantryID)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// ProcessScheduledAlerts processes scheduled expiration alerts for all active configurations
func (uc *ExpirationUsecase) ProcessScheduledAlerts(ctx context.Context) error {
	configs, err := uc.alertConfigRepo.GetActiveConfigurations()
	if err != nil {
		return err
	}

	uc.logger.Info("Processing scheduled expiration alerts", map[string]interface{}{
		"config_count": len(configs),
	})

	var processErrors []error
	alertsSent := 0

	for _, config := range configs {
		if !config.Enabled {
			continue
		}

		// Check if it's time to check this configuration
		if config.LastChecked != nil {
			// Don't check more than once per hour
			if time.Since(*config.LastChecked) < time.Hour {
				continue
			}
		}

		// Process alerts for this configuration
		sent, err := uc.processConfigurationAlerts(ctx, config)
		if err != nil {
			processErrors = append(processErrors, err)
			continue
		}

		alertsSent += sent

		// Update last checked timestamp
		config.UpdateLastChecked()
		uc.alertConfigRepo.Update(config)
	}

	uc.logger.Info("Scheduled alerts processing completed", map[string]interface{}{
		"configs_processed": len(configs),
		"alerts_sent":       alertsSent,
		"errors":            len(processErrors),
	})

	if len(processErrors) > 0 {
		return fmt.Errorf("failed to process %d configurations", len(processErrors))
	}

	return nil
}

// processGlobalUserAlerts processes alerts for all pantries accessible to a user
func (uc *ExpirationUsecase) processGlobalUserAlerts(ctx context.Context, config *domain.AlertConfiguration, req *domain.ExpirationTrackingRequest) (int, error) {
	// Get all pantries the user has access to (owned + member)
	pantries, _, err := uc.pantryRepo.GetUserPantries(config.UserID, 1, 1000) // Get up to 1000 pantries
	if err != nil {
		uc.logger.Error("Failed to get user pantries for global alerts", err, map[string]interface{}{
			"user_id":   config.UserID,
			"config_id": config.ID,
		})
		return 0, err
	}

	totalAlertsSent := 0
	var processErrors []error

	for _, pantry := range pantries {
		// Check if user has permission to view items in this pantry
		canView, err := uc.authzService.CheckPermission(config.UserID, pantry.ID, domain.PermissionViewItems)
		if err != nil {
			uc.logger.Error("Failed to check view permission for global alerts", err, map[string]interface{}{
				"user_id":   config.UserID,
				"pantry_id": pantry.ID,
			})
			processErrors = append(processErrors, err)
			continue
		}

		if !canView {
			continue // Skip pantries where user doesn't have view permission
		}

		// Process alerts for this pantry
		response, err := uc.TrackExpiringItems(ctx, config.UserID, pantry.ID, req)
		if err != nil {
			uc.logger.Error("Failed to track expiring items for global alerts", err, map[string]interface{}{
				"user_id":   config.UserID,
				"pantry_id": pantry.ID,
				"config_id": config.ID,
			})
			processErrors = append(processErrors, err)
			continue
		}

		totalAlertsSent += response.AlertsSent
	}

	uc.logger.Info("Global user alerts processed", map[string]interface{}{
		"user_id":           config.UserID,
		"config_id":         config.ID,
		"pantries_checked":  len(pantries),
		"total_alerts_sent": totalAlertsSent,
		"errors":            len(processErrors),
	})

	// Return total alerts sent even if some pantries had errors
	return totalAlertsSent, nil
}

// Helper methods

// analyzeItemExpiration analyzes an item's expiration status
func (uc *ExpirationUsecase) analyzeItemExpiration(item *domain.InventoryItem, req *domain.ExpirationTrackingRequest, now time.Time) (*domain.ExpiringInventoryItem, domain.ExpirationStatus) {
	if item.ExpirationDate == nil {
		return nil, ""
	}

	daysUntilExpiry := int(item.ExpirationDate.Sub(now).Hours() / 24)

	var status domain.ExpirationStatus
	var actions []domain.ExpirationAction

	if daysUntilExpiry < 0 {
		// Already expired
		status = domain.ExpirationStatusExpired
		actions = []domain.ExpirationAction{
			{Type: "discard", Description: "Remove expired item immediately", Priority: 1},
			{Type: "check", Description: "Inspect for safety before disposal", Priority: 2},
		}
	} else if daysUntilExpiry <= req.CriticalDays {
		// Critical - expiring very soon
		status = domain.ExpirationStatusCritical
		actions = []domain.ExpirationAction{
			{Type: "consume", Description: "Use immediately or today", Priority: 1},
			{Type: "freeze", Description: "Freeze if possible to extend life", Priority: 2},
			{Type: "donate", Description: "Donate if still good", Priority: 3},
		}
	} else if daysUntilExpiry <= req.AlertDays {
		// Alert - expiring soon
		status = domain.ExpirationStatusAlert
		actions = []domain.ExpirationAction{
			{Type: "consume", Description: "Plan to use within next few days", Priority: 1},
			{Type: "freeze", Description: "Consider freezing to extend shelf life", Priority: 2},
		}
	} else if daysUntilExpiry <= req.WarningDays {
		// Warning - expiring in warning period
		status = domain.ExpirationStatusWarning
		actions = []domain.ExpirationAction{
			{Type: "consume", Description: "Include in upcoming meal plans", Priority: 1},
			{Type: "monitor", Description: "Keep an eye on this item", Priority: 2},
		}
	} else {
		// Not expiring within any threshold
		return nil, ""
	}

	expiringItem := &domain.ExpiringInventoryItem{
		ItemID:           item.ID,
		ProductVariantID: item.ProductVariantID,
		Quantity:         item.Quantity,
		UnitOfMeasureID:  item.UnitOfMeasureID,
		ExpirationDate:   item.ExpirationDate.Format("2006-01-02"),
		DaysUntilExpiry:  daysUntilExpiry,
		ExpirationStatus: status,
		LocationID:       item.LocationID,
		PurchasePrice:    item.PurchasePrice,
		EstimatedValue:   item.Quantity * uc.getEstimatedUnitPrice(item),
		Actions:          actions,
	}

	return expiringItem, status
}

// enrichExpiringItem enriches an expiring item with product details
func (uc *ExpirationUsecase) enrichExpiringItem(item *domain.ExpiringInventoryItem) (*domain.ExpiringInventoryItem, error) {
	// Get product variant details
	variant, err := uc.productVariantRepo.GetByID(item.ProductVariantID)
	if err != nil {
		return nil, err
	}

	// Get product details
	product, err := uc.productRepo.GetByID(variant.ProductID)
	if err != nil {
		return nil, err
	}

	// Get unit of measure details
	unit, err := uc.unitRepo.GetByID(item.UnitOfMeasureID)
	if err != nil {
		return nil, err
	}

	// Set product details
	item.ProductName = product.GetDisplayName() // Includes brand if available
	item.VariantName = variant.Name
	item.CategoryID = product.CategoryID
	item.CategoryName = "Category" // We'd need category repository to get actual name
	item.UnitName = unit.Name
	item.UnitSymbol = unit.Symbol

	// Get location name if location ID is provided
	if item.LocationID != nil {
		location, err := uc.pantryLocationRepo.GetByID(*item.LocationID)
		if err != nil {
			// Log error but don't fail the entire operation
			uc.logger.Error("Failed to get location name for expiring item", err, map[string]interface{}{
				"item_id":     item.ItemID,
				"location_id": *item.LocationID,
			})
			// Use fallback name
			locationName := "Unknown Location"
			item.LocationName = &locationName
		} else {
			item.LocationName = &location.Name
		}
	}

	return item, nil
}

// calculateExpirationSummary calculates summary statistics for expiring items
func (uc *ExpirationUsecase) calculateExpirationSummary(items []domain.ExpiringInventoryItem) domain.ExpirationSummary {
	summary := domain.ExpirationSummary{
		TopCategories:      make([]domain.CategoryExpirationSummary, 0),
		RecommendedActions: make([]string, 0),
	}

	categoryMap := make(map[uuid.UUID]*domain.CategoryExpirationSummary)

	for _, item := range items {
		// Calculate values by status
		switch item.ExpirationStatus {
		case domain.ExpirationStatusWarning:
			summary.WarningValue += item.EstimatedValue
		case domain.ExpirationStatusAlert:
			summary.AlertValue += item.EstimatedValue
		case domain.ExpirationStatusCritical:
			summary.CriticalValue += item.EstimatedValue
		case domain.ExpirationStatusExpired:
			summary.ExpiredValue += item.EstimatedValue
		}

		summary.TotalValue += item.EstimatedValue

		// Update category summary
		if catSummary, exists := categoryMap[item.CategoryID]; exists {
			catSummary.ItemCount++
			catSummary.TotalValue += item.EstimatedValue
			// Update worst status
			if uc.getStatusPriority(item.ExpirationStatus) > uc.getStatusPriority(catSummary.WorstStatus) {
				catSummary.WorstStatus = item.ExpirationStatus
			}
		} else {
			categoryMap[item.CategoryID] = &domain.CategoryExpirationSummary{
				CategoryID:   item.CategoryID,
				CategoryName: item.CategoryName,
				ItemCount:    1,
				TotalValue:   item.EstimatedValue,
				WorstStatus:  item.ExpirationStatus,
			}
		}
	}

	// Convert category map to slice
	for _, catSummary := range categoryMap {
		summary.TopCategories = append(summary.TopCategories, *catSummary)
	}

	// Generate recommended actions
	if summary.ExpiredValue > 0 {
		summary.RecommendedActions = append(summary.RecommendedActions, "Remove expired items immediately")
	}
	if summary.CriticalValue > 0 {
		summary.RecommendedActions = append(summary.RecommendedActions, "Use critical items today")
	}
	if summary.AlertValue > 0 {
		summary.RecommendedActions = append(summary.RecommendedActions, "Plan meals with expiring items")
	}
	if summary.WarningValue > 0 {
		summary.RecommendedActions = append(summary.RecommendedActions, "Monitor items approaching expiration")
	}

	return summary
}

// createExpirationNotifications creates notifications for expiring items
func (uc *ExpirationUsecase) createExpirationNotifications(userID, pantryID uuid.UUID, pantryName string, item *domain.ExpiringInventoryItem, channels []domain.NotificationChannel) []*domain.Notification {
	var notifications []*domain.Notification

	// Determine notification type based on expiration status
	var notificationType domain.NotificationType
	var priority domain.NotificationPriority

	switch item.ExpirationStatus {
	case domain.ExpirationStatusExpired:
		notificationType = domain.NotificationTypeExpirationCritical
		priority = domain.NotificationPriorityCritical
	case domain.ExpirationStatusCritical:
		notificationType = domain.NotificationTypeExpirationCritical
		priority = domain.NotificationPriorityCritical
	case domain.ExpirationStatusAlert:
		notificationType = domain.NotificationTypeExpirationAlert
		priority = domain.NotificationPriorityHigh
	case domain.ExpirationStatusWarning:
		notificationType = domain.NotificationTypeExpirationWarning
		priority = domain.NotificationPriorityMedium
	default:
		return notifications
	}

	// Create notification data
	data := map[string]interface{}{
		"item_id":           item.ItemID,
		"product_name":      item.ProductName,
		"variant_name":      item.VariantName,
		"quantity":          item.Quantity,
		"unit_symbol":       item.UnitSymbol,
		"expiration_date":   item.ExpirationDate,
		"days_until_expiry": item.DaysUntilExpiry,
		"pantry_name":       pantryName,
		"estimated_value":   item.EstimatedValue,
		"actions":           item.Actions,
	}

	// Create notifications for each requested channel
	for _, channel := range channels {
		template, exists := domain.NotificationTemplates[notificationType][channel]
		if !exists {
			continue
		}

		// Render title and message
		title, _ := uc.renderTemplate(template.Title, data)
		message, _ := uc.renderTemplate(template.Message, data)

		notification := domain.NewNotification(
			userID,
			&pantryID,
			notificationType,
			channel,
			priority,
			title,
			message,
			data,
			nil, // Send immediately
		)

		notifications = append(notifications, notification)
	}

	return notifications
}

// processConfigurationAlerts processes alerts for a specific configuration
func (uc *ExpirationUsecase) processConfigurationAlerts(ctx context.Context, config *domain.AlertConfiguration) (int, error) {
	// Create tracking request from configuration
	req := &domain.ExpirationTrackingRequest{
		WarningDays:  config.WarningDays,
		AlertDays:    config.AlertDays,
		CriticalDays: config.CriticalDays,
		CategoryIDs:  config.CategoryFilters,
		Channels:     config.Channels,
		SendAlerts:   true,
	}

	if config.PantryID != nil {
		// Process alerts for specific pantry
		pantryID := *config.PantryID
		response, err := uc.TrackExpiringItems(ctx, config.UserID, pantryID, req)
		if err != nil {
			return 0, err
		}
		return response.AlertsSent, nil
	} else {
		// Handle global user alerts across all pantries
		return uc.processGlobalUserAlerts(ctx, config, req)
	}
}

// Helper utility methods

func (uc *ExpirationUsecase) getEstimatedUnitPrice(item *domain.InventoryItem) float64 {
	if item.PurchasePrice != nil {
		return *item.PurchasePrice / item.Quantity
	}
	return 0.0 // No price information available
}

func (uc *ExpirationUsecase) getStatusPriority(status domain.ExpirationStatus) int {
	switch status {
	case domain.ExpirationStatusExpired:
		return 4
	case domain.ExpirationStatusCritical:
		return 3
	case domain.ExpirationStatusAlert:
		return 2
	case domain.ExpirationStatusWarning:
		return 1
	default:
		return 0
	}
}

func (uc *ExpirationUsecase) renderTemplate(templateStr string, data map[string]interface{}) (string, error) {
	// Basic template rendering implementation
	// Production implementation should use:
	// - text/template or html/template for robust template processing
	// - Template caching for performance
	// - Security measures to prevent template injection
	// - Support for complex template logic (loops, conditionals)
	//
	// For now, implement simple {{.key}} replacement
	result := templateStr
	for key, value := range data {
		placeholder := "{{." + key + "}}"
		var replacement string
		switch v := value.(type) {
		case string:
			replacement = v
		case int:
			replacement = fmt.Sprintf("%d", v)
		case float64:
			replacement = fmt.Sprintf("%.2f", v)
		default:
			replacement = fmt.Sprintf("%v", v)
		}
		result = strings.ReplaceAll(result, placeholder, replacement)
	}
	return result, nil
}

func (uc *ExpirationUsecase) isNotFoundError(err error) bool {
	if appErr, ok := err.(*errors.AppError); ok {
		return appErr.Code == errors.ErrCodeNotFound
	}
	return false
}
