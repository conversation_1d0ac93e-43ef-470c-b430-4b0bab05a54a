package usecases

import (
	"context"

	"github.com/wongpinter/pantry-pal/internal/core/domain"
)

// SimpleEventDispatcher is a basic implementation of EventDispatcher
type SimpleEventDispatcher struct {
	logger Logger
}

// NewSimpleEventDispatcher creates a new simple event dispatcher
func NewSimpleEventDispatcher(logger Logger) *SimpleEventDispatcher {
	return &SimpleEventDispatcher{
		logger: logger,
	}
}

// DispatchEvents dispatches domain events synchronously
func (d *SimpleEventDispatcher) DispatchEvents(ctx context.Context, events []domain.DomainEvent) {
	for _, event := range events {
		d.handleEvent(ctx, event)
	}
}

// handleEvent handles a single domain event
func (d *SimpleEventDispatcher) handleEvent(ctx context.Context, event domain.DomainEvent) {
	// For now, just log the events
	// In the future, this can be extended to handle specific event types
	d.logger.LogBusinessEvent(
		event.GetEventType(),
		event.GetAggregateID().String(),
		event.GetEventData(),
	)

	// Handle specific event types
	switch e := event.(type) {
	case *domain.InventoryItemCreatedEvent:
		d.handleInventoryItemCreated(ctx, e)
	case *domain.InventoryItemConsumedEvent:
		d.handleInventoryItemConsumed(ctx, e)
	case *domain.InventoryItemRestockedEvent:
		d.handleInventoryItemRestocked(ctx, e)
	case *domain.InventoryItemDeletedEvent:
		d.handleInventoryItemDeleted(ctx, e)
	default:
		// Log unknown event types
		d.logger.Debug("Unknown event type dispatched", map[string]interface{}{
			"event_type":   event.GetEventType(),
			"aggregate_id": event.GetAggregateID(),
		})
	}
}

// handleInventoryItemCreated handles inventory item created events
func (d *SimpleEventDispatcher) handleInventoryItemCreated(_ context.Context, event *domain.InventoryItemCreatedEvent) {
	d.logger.Info("Inventory item created", map[string]interface{}{
		"item_id":            event.GetAggregateID(),
		"pantry_id":          event.GetEventData()["pantry_id"],
		"product_variant_id": event.GetEventData()["product_variant_id"],
		"quantity":           event.GetEventData()["quantity"],
	})
}

// handleInventoryItemConsumed handles inventory item consumed events
func (d *SimpleEventDispatcher) handleInventoryItemConsumed(_ context.Context, event *domain.InventoryItemConsumedEvent) {
	d.logger.Info("Inventory item consumed", map[string]interface{}{
		"item_id":           event.GetAggregateID(),
		"consumed_quantity": event.GetEventData()["consumed_quantity"],
		"remaining":         event.GetEventData()["new_quantity"],
	})
}

// handleInventoryItemRestocked handles inventory item restocked events
func (d *SimpleEventDispatcher) handleInventoryItemRestocked(_ context.Context, event *domain.InventoryItemRestockedEvent) {
	d.logger.Info("Inventory item restocked", map[string]interface{}{
		"item_id":        event.GetAggregateID(),
		"added_quantity": event.GetEventData()["added_quantity"],
		"new_total":      event.GetEventData()["new_quantity"],
	})
}

// handleInventoryItemDeleted handles inventory item deleted events
func (d *SimpleEventDispatcher) handleInventoryItemDeleted(_ context.Context, event *domain.InventoryItemDeletedEvent) {
	d.logger.Info("Inventory item deleted", map[string]interface{}{
		"item_id":            event.GetAggregateID(),
		"pantry_id":          event.GetEventData()["pantry_id"],
		"product_variant_id": event.GetEventData()["product_variant_id"],
	})
}
