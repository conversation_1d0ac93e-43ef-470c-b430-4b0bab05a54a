package domain

import (
	"time"

	"github.com/google/uuid"
)

// Recipe represents a cooking recipe
type Recipe struct {
	ID           uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID       uuid.UUID       `json:"user_id" gorm:"type:uuid;not null;index"`
	Title        string          `json:"title" gorm:"not null;size:200"`
	Description  *string         `json:"description,omitempty" gorm:"type:text"`
	Cuisine      *string         `json:"cuisine,omitempty" gorm:"size:100"`
	Category     *string         `json:"category,omitempty" gorm:"size:100"`
	Difficulty   DifficultyLevel `json:"difficulty" gorm:"type:varchar(20);default:'medium'"`
	PrepTime     *int            `json:"prep_time,omitempty"`  // minutes
	CookTime     *int            `json:"cook_time,omitempty"`  // minutes
	TotalTime    *int            `json:"total_time,omitempty"` // minutes
	Servings     int             `json:"servings" gorm:"default:4"`
	Calories     *int            `json:"calories,omitempty"`
	IsPublic     bool            `json:"is_public" gorm:"default:false"`
	IsFavorite   bool            `json:"is_favorite" gorm:"default:false"`
	Rating       *float64        `json:"rating,omitempty"` // 1-5 stars
	CookCount    int             `json:"cook_count" gorm:"default:0"`
	LastCookedAt *time.Time      `json:"last_cooked_at,omitempty"`
	Source       *string         `json:"source,omitempty" gorm:"size:500"` // URL or book reference
	Notes        *string         `json:"notes,omitempty" gorm:"type:text"`
	CreatedAt    time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	User         User                `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Ingredients  []RecipeIngredient  `json:"ingredients,omitempty" gorm:"foreignKey:RecipeID;constraint:OnDelete:CASCADE"`
	Instructions []RecipeInstruction `json:"instructions,omitempty" gorm:"foreignKey:RecipeID;constraint:OnDelete:CASCADE"`
	Media        []RecipeMedia       `json:"media,omitempty" gorm:"foreignKey:RecipeID;constraint:OnDelete:CASCADE"`
	Nutrition    *RecipeNutrition    `json:"nutrition,omitempty" gorm:"foreignKey:RecipeID;constraint:OnDelete:CASCADE"`
	Reviews      []RecipeReview      `json:"reviews,omitempty" gorm:"foreignKey:RecipeID;constraint:OnDelete:CASCADE"`
	Tags         []RecipeTag         `json:"tags,omitempty" gorm:"many2many:recipe_recipe_tags;"`
}

// RecipeIngredient represents an ingredient in a recipe
type RecipeIngredient struct {
	ID               uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RecipeID         uuid.UUID  `json:"recipe_id" gorm:"type:uuid;not null;index"`
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty" gorm:"type:uuid;index"`
	Name             string     `json:"name" gorm:"not null;size:200"` // fallback if no product variant
	Quantity         float64    `json:"quantity" gorm:"not null"`
	UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty" gorm:"type:uuid"`
	Unit             *string    `json:"unit,omitempty" gorm:"size:50"`         // fallback unit
	Preparation      *string    `json:"preparation,omitempty" gorm:"size:200"` // "diced", "chopped", etc.
	IsOptional       bool       `json:"is_optional" gorm:"default:false"`
	IsGarnish        bool       `json:"is_garnish" gorm:"default:false"`
	Order            int        `json:"order" gorm:"not null"`
	Notes            *string    `json:"notes,omitempty" gorm:"size:500"`
	CreatedAt        time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt        time.Time  `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipe         Recipe          `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
	ProductVariant *ProductVariant `json:"product_variant,omitempty" gorm:"foreignKey:ProductVariantID"`
	UnitOfMeasure  *UnitOfMeasure  `json:"unit_of_measure,omitempty" gorm:"foreignKey:UnitOfMeasureID"`
}

// RecipeInstruction represents a cooking instruction step
type RecipeInstruction struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RecipeID    uuid.UUID `json:"recipe_id" gorm:"type:uuid;not null;index"`
	StepNumber  int       `json:"step_number" gorm:"not null"`
	Title       *string   `json:"title,omitempty" gorm:"size:200"`
	Instruction string    `json:"instruction" gorm:"not null;type:text"`
	Duration    *int      `json:"duration,omitempty"`    // minutes for this step
	Temperature *int      `json:"temperature,omitempty"` // celsius
	ImageURL    *string   `json:"image_url,omitempty" gorm:"size:500"`
	VideoURL    *string   `json:"video_url,omitempty" gorm:"size:500"`
	Tips        *string   `json:"tips,omitempty" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipe Recipe `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
}

// RecipeMedia represents media files associated with a recipe
type RecipeMedia struct {
	ID        uuid.UUID       `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RecipeID  uuid.UUID       `json:"recipe_id" gorm:"type:uuid;not null;index"`
	Type      RecipeMediaType `json:"type" gorm:"type:varchar(20);not null"`
	URL       string          `json:"url" gorm:"not null;size:500"`
	Filename  string          `json:"filename" gorm:"not null;size:255"`
	Size      int64           `json:"size"` // bytes
	MimeType  string          `json:"mime_type" gorm:"size:100"`
	Width     *int            `json:"width,omitempty"`
	Height    *int            `json:"height,omitempty"`
	Duration  *int            `json:"duration,omitempty"` // seconds for videos
	IsMain    bool            `json:"is_main" gorm:"default:false"`
	Order     int             `json:"order" gorm:"default:0"`
	Caption   *string         `json:"caption,omitempty" gorm:"size:500"`
	AltText   *string         `json:"alt_text,omitempty" gorm:"size:200"`
	CreatedAt time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipe Recipe `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
}

// RecipeNutrition represents nutritional information for a recipe
type RecipeNutrition struct {
	ID            uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RecipeID      uuid.UUID `json:"recipe_id" gorm:"type:uuid;not null;unique;index"`
	Calories      *int      `json:"calories,omitempty"`
	Protein       *float64  `json:"protein,omitempty"`       // grams
	Carbohydrates *float64  `json:"carbohydrates,omitempty"` // grams
	Fat           *float64  `json:"fat,omitempty"`           // grams
	Fiber         *float64  `json:"fiber,omitempty"`         // grams
	Sugar         *float64  `json:"sugar,omitempty"`         // grams
	Sodium        *float64  `json:"sodium,omitempty"`        // milligrams
	Cholesterol   *float64  `json:"cholesterol,omitempty"`   // milligrams
	VitaminA      *float64  `json:"vitamin_a,omitempty"`     // IU
	VitaminC      *float64  `json:"vitamin_c,omitempty"`     // milligrams
	Calcium       *float64  `json:"calcium,omitempty"`       // milligrams
	Iron          *float64  `json:"iron,omitempty"`          // milligrams
	ServingSize   *string   `json:"serving_size,omitempty" gorm:"size:100"`
	CreatedAt     time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipe Recipe `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
}

// TableName overrides the table name used by RecipeNutrition to the singular form
func (RecipeNutrition) TableName() string {
	return "recipe_nutrition"
}

// RecipeTag represents tags for categorizing recipes
type RecipeTag struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name        string    `json:"name" gorm:"not null;unique;size:100"`
	Description *string   `json:"description,omitempty" gorm:"size:500"`
	Color       *string   `json:"color,omitempty" gorm:"size:7"` // hex color
	Icon        *string   `json:"icon,omitempty" gorm:"size:50"`
	IsSystem    bool      `json:"is_system" gorm:"default:false"` // system-defined tags
	UsageCount  int       `json:"usage_count" gorm:"default:0"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipes []Recipe `json:"recipes,omitempty" gorm:"many2many:recipe_recipe_tags;"`
}

// RecipeReview represents user reviews and ratings for recipes
type RecipeReview struct {
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	RecipeID  uuid.UUID  `json:"recipe_id" gorm:"type:uuid;not null;index"`
	UserID    uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"`
	Rating    int        `json:"rating" gorm:"not null;check:rating >= 1 AND rating <= 5"`
	Comment   *string    `json:"comment,omitempty" gorm:"type:text"`
	ImageURL  *string    `json:"image_url,omitempty" gorm:"size:500"`
	CookedAt  *time.Time `json:"cooked_at,omitempty"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Recipe Recipe `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
	User   User   `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// RecipeCollection represents collections of recipes (meal plans, favorites, etc.)
type RecipeCollection struct {
	ID          uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID      uuid.UUID `json:"user_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"not null;size:200"`
	Description *string   `json:"description,omitempty" gorm:"type:text"`
	IsPublic    bool      `json:"is_public" gorm:"default:false"`
	ImageURL    *string   `json:"image_url,omitempty" gorm:"size:500"`
	CreatedAt   time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	User    User                   `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Recipes []RecipeCollectionItem `json:"recipes,omitempty" gorm:"foreignKey:CollectionID;constraint:OnDelete:CASCADE"`
}

// RecipeCollectionItem represents a recipe in a collection
type RecipeCollectionItem struct {
	ID           uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CollectionID uuid.UUID `json:"collection_id" gorm:"type:uuid;not null;index"`
	RecipeID     uuid.UUID `json:"recipe_id" gorm:"type:uuid;not null;index"`
	Order        int       `json:"order" gorm:"default:0"`
	Notes        *string   `json:"notes,omitempty" gorm:"size:500"`
	AddedAt      time.Time `json:"added_at" gorm:"autoCreateTime"`

	// Relationships
	Collection RecipeCollection `json:"collection,omitempty" gorm:"foreignKey:CollectionID"`
	Recipe     Recipe           `json:"recipe,omitempty" gorm:"foreignKey:RecipeID"`
}

// Enums
type DifficultyLevel string

const (
	DifficultyEasy   DifficultyLevel = "easy"
	DifficultyMedium DifficultyLevel = "medium"
	DifficultyHard   DifficultyLevel = "hard"
	DifficultyExpert DifficultyLevel = "expert"
)

type RecipeMediaType string

const (
	RecipeMediaTypeImage RecipeMediaType = "image"
	RecipeMediaTypeVideo RecipeMediaType = "video"
)

// Recipe request/response types
type CreateRecipeRequest struct {
	Title        string                           `json:"title" validate:"required,min=3,max=200"`
	Description  *string                          `json:"description,omitempty" validate:"omitempty,max=1000"`
	Cuisine      *string                          `json:"cuisine,omitempty" validate:"omitempty,max=100"`
	Category     *string                          `json:"category,omitempty" validate:"omitempty,max=100"`
	Difficulty   DifficultyLevel                  `json:"difficulty" validate:"omitempty,oneof=easy medium hard expert"`
	PrepTime     *int                             `json:"prep_time,omitempty" validate:"omitempty,min=0,max=1440"`
	CookTime     *int                             `json:"cook_time,omitempty" validate:"omitempty,min=0,max=1440"`
	Servings     int                              `json:"servings" validate:"required,min=1,max=100"`
	IsPublic     bool                             `json:"is_public"`
	Source       *string                          `json:"source,omitempty" validate:"omitempty,max=500"`
	Notes        *string                          `json:"notes,omitempty" validate:"omitempty,max=2000"`
	Tags         []string                         `json:"tags,omitempty"`
	Ingredients  []CreateRecipeIngredientRequest  `json:"ingredients" validate:"required,min=1,dive"`
	Instructions []CreateRecipeInstructionRequest `json:"instructions" validate:"required,min=1,dive"`
	Nutrition    *CreateRecipeNutritionRequest    `json:"nutrition,omitempty"`
}

type CreateRecipeIngredientRequest struct {
	ProductVariantID *uuid.UUID `json:"product_variant_id,omitempty"`
	Name             string     `json:"name" validate:"required,min=1,max=200"`
	Quantity         float64    `json:"quantity" validate:"required,gt=0"`
	UnitOfMeasureID  *uuid.UUID `json:"unit_of_measure_id,omitempty"`
	Unit             *string    `json:"unit,omitempty" validate:"omitempty,max=50"`
	Preparation      *string    `json:"preparation,omitempty" validate:"omitempty,max=200"`
	IsOptional       bool       `json:"is_optional"`
	IsGarnish        bool       `json:"is_garnish"`
	Notes            *string    `json:"notes,omitempty" validate:"omitempty,max=500"`
}

type CreateRecipeInstructionRequest struct {
	Title       *string `json:"title,omitempty" validate:"omitempty,max=200"`
	Instruction string  `json:"instruction" validate:"required,min=10,max=2000"`
	Duration    *int    `json:"duration,omitempty" validate:"omitempty,min=0,max=1440"`
	Temperature *int    `json:"temperature,omitempty" validate:"omitempty,min=-50,max=500"`
	Tips        *string `json:"tips,omitempty" validate:"omitempty,max=1000"`
}

type CreateRecipeNutritionRequest struct {
	Calories      *int     `json:"calories,omitempty" validate:"omitempty,min=0,max=10000"`
	Protein       *float64 `json:"protein,omitempty" validate:"omitempty,min=0,max=1000"`
	Carbohydrates *float64 `json:"carbohydrates,omitempty" validate:"omitempty,min=0,max=1000"`
	Fat           *float64 `json:"fat,omitempty" validate:"omitempty,min=0,max=1000"`
	Fiber         *float64 `json:"fiber,omitempty" validate:"omitempty,min=0,max=1000"`
	Sugar         *float64 `json:"sugar,omitempty" validate:"omitempty,min=0,max=1000"`
	Sodium        *float64 `json:"sodium,omitempty" validate:"omitempty,min=0,max=10000"`
	ServingSize   *string  `json:"serving_size,omitempty" validate:"omitempty,max=100"`
}

type UpdateRecipeRequest struct {
	Title        *string                          `json:"title,omitempty" validate:"omitempty,min=3,max=200"`
	Description  *string                          `json:"description,omitempty" validate:"omitempty,max=1000"`
	Cuisine      *string                          `json:"cuisine,omitempty" validate:"omitempty,max=100"`
	Category     *string                          `json:"category,omitempty" validate:"omitempty,max=100"`
	Difficulty   *DifficultyLevel                 `json:"difficulty,omitempty" validate:"omitempty,oneof=easy medium hard expert"`
	PrepTime     *int                             `json:"prep_time,omitempty" validate:"omitempty,min=0,max=1440"`
	CookTime     *int                             `json:"cook_time,omitempty" validate:"omitempty,min=0,max=1440"`
	Servings     *int                             `json:"servings,omitempty" validate:"omitempty,min=1,max=100"`
	IsPublic     *bool                            `json:"is_public,omitempty"`
	IsFavorite   *bool                            `json:"is_favorite,omitempty"`
	Source       *string                          `json:"source,omitempty" validate:"omitempty,max=500"`
	Notes        *string                          `json:"notes,omitempty" validate:"omitempty,max=2000"`
	Tags         []string                         `json:"tags,omitempty"`
	Ingredients  []CreateRecipeIngredientRequest  `json:"ingredients,omitempty" validate:"omitempty,dive"`
	Instructions []CreateRecipeInstructionRequest `json:"instructions,omitempty" validate:"omitempty,dive"`
	Nutrition    *CreateRecipeNutritionRequest    `json:"nutrition,omitempty"`
}

type RecipeQueryParams struct {
	Page        int      `query:"page" validate:"omitempty,min=1"`
	Limit       int      `query:"limit" validate:"omitempty,min=1,max=100"`
	Search      string   `query:"search"`
	Cuisine     string   `query:"cuisine"`
	Category    string   `query:"category"`
	Difficulty  string   `query:"difficulty"`
	MaxPrepTime int      `query:"max_prep_time"`
	MaxCookTime int      `query:"max_cook_time"`
	MaxCalories int      `query:"max_calories"`
	Tags        []string `query:"tags"`
	IsFavorite  bool     `query:"is_favorite"`
	IsPublic    bool     `query:"is_public"`
	SortBy      string   `query:"sort_by" validate:"omitempty,oneof=created_at updated_at title rating cook_count"`
	SortOrder   string   `query:"sort_order" validate:"omitempty,oneof=asc desc"`
}

type ScaleRecipeRequest struct {
	Servings int `json:"servings" validate:"required,min=1,max=100"`
}

type RateRecipeRequest struct {
	Rating   int     `json:"rating" validate:"required,min=1,max=5"`
	Comment  *string `json:"comment,omitempty" validate:"omitempty,max=2000"`
	CookedAt *string `json:"cooked_at,omitempty"` // ISO 8601 format
}

type InventoryCheckRequest struct {
	PantryID *uuid.UUID `json:"pantry_id,omitempty"`
}

type InventoryCheckResponse struct {
	AvailableIngredients []IngredientAvailability `json:"available_ingredients"`
	MissingIngredients   []IngredientAvailability `json:"missing_ingredients"`
	ShoppingListItems    []ShoppingListItem       `json:"shopping_list_items,omitempty"`
	CanCook              bool                     `json:"can_cook"`
	MissingCount         int                      `json:"missing_count"`
	TotalIngredients     int                      `json:"total_ingredients"`
}

type IngredientAvailability struct {
	IngredientID      uuid.UUID  `json:"ingredient_id"`
	Name              string     `json:"name"`
	RequiredQuantity  float64    `json:"required_quantity"`
	RequiredUnit      string     `json:"required_unit"`
	AvailableQuantity *float64   `json:"available_quantity,omitempty"`
	AvailableUnit     *string    `json:"available_unit,omitempty"`
	IsAvailable       bool       `json:"is_available"`
	IsOptional        bool       `json:"is_optional"`
	ProductVariantID  *uuid.UUID `json:"product_variant_id,omitempty"`
}

// Repository interfaces
type RecipeRepository interface {
	Create(recipe *Recipe) error
	GetByID(id uuid.UUID) (*Recipe, error)
	GetByUserID(userID uuid.UUID, params RecipeQueryParams) ([]*Recipe, int64, error)
	GetPublicRecipes(params RecipeQueryParams) ([]*Recipe, int64, error)
	Update(recipe *Recipe) error
	Delete(id uuid.UUID) error
	GetByTags(tags []string, params RecipeQueryParams) ([]*Recipe, int64, error)
	GetFavorites(userID uuid.UUID, params RecipeQueryParams) ([]*Recipe, int64, error)
	Search(query string, params RecipeQueryParams) ([]*Recipe, int64, error)
	GetPopular(limit int) ([]*Recipe, error)
	GetRecentlyCooked(userID uuid.UUID, limit int) ([]*Recipe, error)
	IncrementCookCount(id uuid.UUID) error
	UpdateLastCookedAt(id uuid.UUID) error
}

type RecipeTagRepository interface {
	Create(tag *RecipeTag) error
	GetByID(id uuid.UUID) (*RecipeTag, error)
	GetByName(name string) (*RecipeTag, error)
	GetAll() ([]*RecipeTag, error)
	GetPopular(limit int) ([]*RecipeTag, error)
	Update(tag *RecipeTag) error
	Delete(id uuid.UUID) error
	IncrementUsage(id uuid.UUID) error
	DecrementUsage(id uuid.UUID) error
}

type RecipeReviewRepository interface {
	Create(review *RecipeReview) error
	GetByID(id uuid.UUID) (*RecipeReview, error)
	GetByRecipeID(recipeID uuid.UUID, page, limit int) ([]*RecipeReview, int64, error)
	GetByUserID(userID uuid.UUID, page, limit int) ([]*RecipeReview, int64, error)
	Update(review *RecipeReview) error
	Delete(id uuid.UUID) error
	GetAverageRating(recipeID uuid.UUID) (float64, error)
	GetRatingDistribution(recipeID uuid.UUID) (map[int]int, error)
}

type RecipeCollectionRepository interface {
	Create(collection *RecipeCollection) error
	GetByID(id uuid.UUID) (*RecipeCollection, error)
	GetByUserID(userID uuid.UUID, page, limit int) ([]*RecipeCollection, int64, error)
	GetPublicCollections(page, limit int) ([]*RecipeCollection, int64, error)
	Update(collection *RecipeCollection) error
	Delete(id uuid.UUID) error
	AddRecipe(collectionID, recipeID uuid.UUID, order int, notes *string) error
	RemoveRecipe(collectionID, recipeID uuid.UUID) error
	GetCollectionRecipes(collectionID uuid.UUID, page, limit int) ([]*Recipe, int64, error)
}

// Business logic methods for Recipe
func NewRecipe(userID uuid.UUID, req *CreateRecipeRequest) *Recipe {
	recipe := &Recipe{
		ID:          uuid.New(),
		UserID:      userID,
		Title:       req.Title,
		Description: req.Description,
		Cuisine:     req.Cuisine,
		Category:    req.Category,
		Difficulty:  req.Difficulty,
		PrepTime:    req.PrepTime,
		CookTime:    req.CookTime,
		Servings:    req.Servings,
		IsPublic:    req.IsPublic,
		Source:      req.Source,
		Notes:       req.Notes,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Calculate total time
	if req.PrepTime != nil && req.CookTime != nil {
		totalTime := *req.PrepTime + *req.CookTime
		recipe.TotalTime = &totalTime
	} else if req.PrepTime != nil {
		recipe.TotalTime = req.PrepTime
	} else if req.CookTime != nil {
		recipe.TotalTime = req.CookTime
	}

	// Create ingredients
	recipe.Ingredients = make([]RecipeIngredient, len(req.Ingredients))
	for i, ing := range req.Ingredients {
		recipe.Ingredients[i] = RecipeIngredient{
			ID:               uuid.New(),
			RecipeID:         recipe.ID,
			ProductVariantID: ing.ProductVariantID,
			Name:             ing.Name,
			Quantity:         ing.Quantity,
			UnitOfMeasureID:  ing.UnitOfMeasureID,
			Unit:             ing.Unit,
			Preparation:      ing.Preparation,
			IsOptional:       ing.IsOptional,
			IsGarnish:        ing.IsGarnish,
			Order:            i + 1,
			Notes:            ing.Notes,
			CreatedAt:        time.Now(),
			UpdatedAt:        time.Now(),
		}
	}

	// Create instructions
	recipe.Instructions = make([]RecipeInstruction, len(req.Instructions))
	for i, inst := range req.Instructions {
		recipe.Instructions[i] = RecipeInstruction{
			ID:          uuid.New(),
			RecipeID:    recipe.ID,
			StepNumber:  i + 1,
			Title:       inst.Title,
			Instruction: inst.Instruction,
			Duration:    inst.Duration,
			Temperature: inst.Temperature,
			Tips:        inst.Tips,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
	}

	// Create nutrition if provided
	if req.Nutrition != nil {
		recipe.Nutrition = &RecipeNutrition{
			ID:            uuid.New(),
			RecipeID:      recipe.ID,
			Calories:      req.Nutrition.Calories,
			Protein:       req.Nutrition.Protein,
			Carbohydrates: req.Nutrition.Carbohydrates,
			Fat:           req.Nutrition.Fat,
			Fiber:         req.Nutrition.Fiber,
			Sugar:         req.Nutrition.Sugar,
			Sodium:        req.Nutrition.Sodium,
			ServingSize:   req.Nutrition.ServingSize,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
	}

	return recipe
}

func (r *Recipe) Update(req *UpdateRecipeRequest) {
	if req.Title != nil {
		r.Title = *req.Title
	}
	if req.Description != nil {
		r.Description = req.Description
	}
	if req.Cuisine != nil {
		r.Cuisine = req.Cuisine
	}
	if req.Category != nil {
		r.Category = req.Category
	}
	if req.Difficulty != nil {
		r.Difficulty = *req.Difficulty
	}
	if req.PrepTime != nil {
		r.PrepTime = req.PrepTime
	}
	if req.CookTime != nil {
		r.CookTime = req.CookTime
	}
	if req.Servings != nil {
		r.Servings = *req.Servings
	}
	if req.IsPublic != nil {
		r.IsPublic = *req.IsPublic
	}
	if req.IsFavorite != nil {
		r.IsFavorite = *req.IsFavorite
	}
	if req.Source != nil {
		r.Source = req.Source
	}
	if req.Notes != nil {
		r.Notes = req.Notes
	}

	// Recalculate total time
	if r.PrepTime != nil && r.CookTime != nil {
		totalTime := *r.PrepTime + *r.CookTime
		r.TotalTime = &totalTime
	} else if r.PrepTime != nil {
		r.TotalTime = r.PrepTime
	} else if r.CookTime != nil {
		r.TotalTime = r.CookTime
	}

	r.UpdatedAt = time.Now()
}

func (r *Recipe) MarkAsCooked() {
	r.CookCount++
	now := time.Now()
	r.LastCookedAt = &now
	r.UpdatedAt = now
}

func (r *Recipe) ScaleIngredients(newServings int) []RecipeIngredient {
	if newServings <= 0 || r.Servings <= 0 {
		return r.Ingredients
	}

	scaleFactor := float64(newServings) / float64(r.Servings)
	scaledIngredients := make([]RecipeIngredient, len(r.Ingredients))

	for i, ingredient := range r.Ingredients {
		scaledIngredients[i] = ingredient
		scaledIngredients[i].Quantity = ingredient.Quantity * scaleFactor
	}

	return scaledIngredients
}

func (r *Recipe) GetEstimatedCaloriesPerServing() *int {
	if r.Calories == nil || r.Servings <= 0 {
		return nil
	}
	caloriesPerServing := *r.Calories / r.Servings
	return &caloriesPerServing
}

func (r *Recipe) GetDifficultyLevel() string {
	return string(r.Difficulty)
}

func (r *Recipe) IsOwnedBy(userID uuid.UUID) bool {
	return r.UserID == userID
}

func (r *Recipe) CanBeViewedBy(userID uuid.UUID) bool {
	return r.IsPublic || r.UserID == userID
}

func (r *Recipe) GetMainImage() *RecipeMedia {
	for _, media := range r.Media {
		if media.Type == RecipeMediaTypeImage && media.IsMain {
			return &media
		}
	}
	// Return first image if no main image is set
	for _, media := range r.Media {
		if media.Type == RecipeMediaTypeImage {
			return &media
		}
	}
	return nil
}

func (r *Recipe) GetVideos() []RecipeMedia {
	var videos []RecipeMedia
	for _, media := range r.Media {
		if media.Type == RecipeMediaTypeVideo {
			videos = append(videos, media)
		}
	}
	return videos
}

// Business logic methods for RecipeTag
func NewRecipeTag(name, description string, color, icon *string) *RecipeTag {
	return &RecipeTag{
		ID:          uuid.New(),
		Name:        name,
		Description: &description,
		Color:       color,
		Icon:        icon,
		IsSystem:    false,
		UsageCount:  0,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

func (rt *RecipeTag) IncrementUsage() {
	rt.UsageCount++
	rt.UpdatedAt = time.Now()
}

func (rt *RecipeTag) DecrementUsage() {
	if rt.UsageCount > 0 {
		rt.UsageCount--
	}
	rt.UpdatedAt = time.Now()
}
